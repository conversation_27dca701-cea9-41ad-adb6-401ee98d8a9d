"""
测试create_chat的正确参数
"""

def test_create_chat():
    """测试create_chat的正确参数"""
    
    print("🧪 测试create_chat的正确参数")
    print("=" * 80)
    
    try:
        from ragflow_sdk import RAGFlow
        
        client = RAGFlow(
            api_key="ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
            base_url="http://10.100.98.181:8011"
        )
        
        print("✅ RAGFlow客户端创建成功")
        
        # 查看create_chat方法的签名
        import inspect
        if hasattr(client, 'create_chat'):
            sig = inspect.signature(client.create_chat)
            print(f"📋 create_chat方法签名: {sig}")
        
        # 尝试不同的参数组合
        test_params = [
            {},
            {"name": "测试会话"},
            {"assistant_id": "3c3d4c94703911f091af0242ac150003"},
            {"id": "3c3d4c94703911f091af0242ac150003"},
            {"agent_id": "3c3d4c94703911f091af0242ac150003"},
            {"name": "测试会话", "assistant_id": "3c3d4c94703911f091af0242ac150003"},
        ]
        
        for i, params in enumerate(test_params, 1):
            try:
                print(f"\n🔄 测试参数组合 {i}: {params}")
                chat = client.create_chat(**params)
                print(f"✅ 成功创建聊天: {chat}")
                break
            except Exception as e:
                print(f"❌ 失败: {str(e)}")
        
        # 尝试查看现有的聊天
        try:
            chats = client.list_chats()
            print(f"\n📋 现有聊天列表: {chats}")
        except Exception as e:
            print(f"❌ 获取聊天列表失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    test_create_chat()
