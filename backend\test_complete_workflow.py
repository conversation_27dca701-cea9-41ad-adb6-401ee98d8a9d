"""
完整工作流程测试
模拟从接收数据到RAGFlow智能体分析的完整流程
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.json_formatter import PolicyJsonFormatter


def simulate_complete_workflow():
    """模拟完整的工作流程"""
    
    print("🚀 完整工作流程测试")
    print("=" * 80)
    
    # 模拟前端发送的请求数据
    request_data = {
        "type": "policy_data",
        "count": 5,
        "data": [
            {
                "id": "164",
                "name": "240409-常州后勤管理系统运维终端-后勤管理系统运维端口-杨浩然-长期",
                "src_zone": "internal",
                "src_addr": ["常州后勤管理系统运维终端"],
                "dst_zone": "external",
                "dst_addr": ["后勤管理系统运维端口"],
                "service": ["TCP 80", "TCP 443"],
                "action": "允许",
                "hit_count": "1500"
            },
            {
                "id": "176",
                "name": "240415-盐城市公司地址-后勤管理系统业务端口-杨浩然-长期",
                "src_zone": "any",
                "src_addr": ["盐城IP地址"],
                "dst_zone": "internal",
                "dst_addr": ["后勤管理系统运维终端"],
                "service": ["TCP 21", "TCP 23"],  # 包含危险端口
                "action": "允许",
                "hit_count": "50000"
            },
            {
                "id": "201",
                "name": "240501-南京办公终端-财务系统接口-李明-长期",
                "src_zone": "dmz",
                "src_addr": ["南京总部5楼办公终端"],
                "dst_zone": "external", 
                "dst_addr": ["财务系统数据接口"],
                "service": ["TCP 443", "TCP 80"],
                "action": "允许",
                "hit_count": "2000"
            },
            {
                "id": "202",
                "name": "240502-苏州服务器集群-ERP系统服务-张伟-短期",
                "src_zone": "management",
                "src_addr": ["苏州园区服务器"],
                "dst_zone": "any",
                "dst_addr": ["ERP系统核心服务"],
                "service": ["TCP 1433", "TCP 3306"],  # 数据库端口
                "action": "允许",
                "hit_count": "800"
            },
            {
                "id": "203",
                "name": "240503-无锡物联终端-智能仓储模块-王芳-长期",
                "src_zone": "cloud",
                "src_addr": ["无锡物流中心物联终端"],
                "dst_zone": "internal",
                "dst_addr": ["智能仓储管理系统"],
                "service": ["ICMP", "TCP 8080"],
                "action": "允许",
                "hit_count": "300"
            }
        ]
    }
    
    print(f"📊 模拟请求数据: {len(request_data['data'])} 个策略")
    print()
    
    try:
        # 步骤1: 数据类型检查（模拟）
        print("🔄 步骤1: 数据类型检查...")
        data_type = request_data.get("type", "")
        if data_type == "policy_data":
            print("✅ 数据类型验证通过: policy_data")
        else:
            print("❌ 数据类型验证失败")
            return
        
        # 步骤2: 解析和格式化策略数据
        print(f"\n🔄 步骤2: 解析和格式化策略数据...")
        policy_data = request_data.get("data", [])
        policy_objects, formatted_json = PolicyJsonFormatter.parse_and_format_policy_data(policy_data)
        
        # 打印格式化结果
        PolicyJsonFormatter.print_formatted_json(formatted_json, "策略数据格式化结果")
        
        # 步骤3: 调用RAGFlow智能体（模拟）
        print(f"\n🔄 步骤3: 调用RAGFlow智能体分析...")
        
        try:
            from services.ragflow_service import policy_analysis_service
            
            print(f"📋 智能体配置:")
            print(f"  - Base URL: {policy_analysis_service.config.RAGFLOW_BASE_URL}")
            print(f"  - Assistant ID: {policy_analysis_service.config.RAGFLOW_ASSISTANT_ID}")
            
            # 尝试调用智能体
            if policy_analysis_service._rag_client:
                print("🤖 开始调用RAGFlow智能体...")
                analysis_result = policy_analysis_service.process_formatted_json(formatted_json)
                
                print(f"\n📊 智能体分析结果:")
                print("=" * 80)
                print(analysis_result)
                print("=" * 80)
                
            else:
                print("⚠️ RAGFlow客户端未初始化，模拟智能体响应...")
                
                # 模拟智能体响应
                mock_analysis = f"""
## 防火墙策略安全分析报告

### 1. 策略配置安全性评估

**总体评估**: 发现多个安全风险需要关注

**策略统计**:
- 总策略数: {len(policy_objects)}
- 允许策略: {len([p for p in policy_objects if 'allow' in str(p).lower() or '允许' in str(p)])}
- 拒绝策略: {len([p for p in policy_objects if 'deny' in str(p).lower() or '拒绝' in str(p)])}

### 2. 潜在安全风险识别

**高风险发现**:
1. **策略ID 176**: 使用了危险端口TCP 21 (FTP)和TCP 23 (Telnet)
   - 风险: 明文传输，容易被窃听和攻击
   - 建议: 使用SFTP (TCP 22)和SSH替代

2. **策略ID 202**: 暴露数据库端口TCP 1433 (SQL Server)和TCP 3306 (MySQL)
   - 风险: 数据库直接暴露，可能遭受SQL注入和暴力破解
   - 建议: 限制源地址范围，使用VPN或专用网络

### 3. 优化建议和最佳实践

1. **端口安全**:
   - 避免使用明文协议端口 (21, 23, 80用于敏感数据)
   - 数据库端口应限制在内网访问
   - 使用加密协议 (HTTPS, SFTP, SSH)

2. **访问控制**:
   - 实施最小权限原则
   - 定期审查策略有效性
   - 使用地理位置限制

3. **监控和审计**:
   - 启用详细日志记录
   - 设置异常流量告警
   - 定期进行安全评估

### 4. 合规性检查建议

1. **网络安全法合规**:
   - 确保关键信息基础设施保护措施到位
   - 建立网络安全事件应急预案

2. **等保合规**:
   - 按照等保要求配置访问控制策略
   - 实施身份认证和授权机制

3. **行业标准**:
   - 参考ISO 27001信息安全管理体系
   - 遵循NIST网络安全框架

### 5. 紧急处理建议

**立即处理**:
- 策略ID 176: 禁用或替换危险端口
- 策略ID 202: 限制数据库访问源

**短期优化**:
- 审查所有允许策略的必要性
- 实施更严格的源地址限制

**长期规划**:
- 建立策略生命周期管理
- 实施零信任网络架构
"""
                
                print(f"\n📊 模拟智能体分析结果:")
                print("=" * 80)
                print(mock_analysis)
                print("=" * 80)
                
        except Exception as e:
            print(f"⚠️ 智能体调用失败: {str(e)}")
        
        # 步骤4: 返回响应
        print(f"\n🔄 步骤4: 构造响应...")
        response = {
            "code": 400,
            "data": "✅ 数据已成功接收、验证并解析，智能体分析完成，请查看后端控制台输出。",
            "msg": "数据处理完成"
        }
        
        print(f"📤 API响应:")
        print(json.dumps(response, ensure_ascii=False, indent=2))
        
        print(f"\n🎉 完整工作流程测试完成!")
        print(f"📋 流程总结:")
        print(f"  1. ✅ 数据类型检查")
        print(f"  2. ✅ 策略数据解析和格式化")
        print(f"  3. ✅ RAGFlow智能体调用（模拟）")
        print(f"  4. ✅ 响应构造")
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    simulate_complete_workflow()
