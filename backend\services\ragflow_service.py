"""
RAGFlow智能体服务模块
专门用于与RAGFlow智能体进行对话
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 尝试导入RAGFlow SDK，如果失败则设置为None
try:
    from ragflow_sdk import RAGFlow
    RAGFLOW_AVAILABLE = True
except ImportError:
    RAGFlow = None
    RAGFLOW_AVAILABLE = False
    logger.warning("RAGFlow SDK 未安装，相关功能将不可用")


class Config:
    """配置类"""

    def __init__(self,
                 api_key: str = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
                 base_url: str = "http://*************:8011",
                 assistant_id: str = "cbaecd286f9211f0b4040242ac150003"):
        """
        初始化配置

        Args:
            api_key: RAGFlow API密钥
            base_url: RAGFlow API地址
            assistant_id: RAGFlow 智能体ID
        """
        # RAGFlow 配置
        self.RAGFLOW_API_KEY = api_key
        self.RAGFLOW_BASE_URL = base_url
        self.RAGFLOW_ASSISTANT_ID = assistant_id

        # 文件配置
        self.MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
        self.ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}


# 默认配置实例
default_config = Config()


class RAGFlowService:
    """RAGFlow智能体服务类"""

    def __init__(self, config: Optional[Config] = None):
        """
        初始化RAGFlow服务

        Args:
            config: 配置实例，如果为None则使用默认配置
        """
        self.config = config or default_config
        self._rag_client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化RAGFlow客户端"""
        if not RAGFLOW_AVAILABLE:
            logger.warning("RAGFlow SDK 不可用，跳过客户端初始化")
            self._rag_client = None
            return

        try:
            self._rag_client = RAGFlow(
                api_key=self.config.RAGFLOW_API_KEY,
                base_url=self.config.RAGFLOW_BASE_URL
            )
            logger.info("RAGFlow 客户端初始化成功")
        except Exception as e:
            logger.error(f"RAGFlow 客户端初始化失败: {str(e)}")
            self._rag_client = None

    def ask_agent(self, prompt: str, agent_id: Optional[str] = None, stream: bool = True) -> str:
        """
        向RAGFlow智能体发送问题并获取回答

        Args:
            prompt: 问题或提示词
            agent_id: 智能体ID，如果为None则使用配置中的默认ID
            stream: 是否使用流式响应

        Returns:
            str: 智能体的回答

        Raises:
            Exception: 当调用失败时抛出异常
        """
        if not RAGFLOW_AVAILABLE:
            raise Exception("RAGFlow SDK 不可用")

        if not self._rag_client:
            raise Exception("RAGFlow 客户端未初始化")

        try:
            agent_id = agent_id or self.config.RAGFLOW_ASSISTANT_ID
            logger.info(f"向智能体 {agent_id} 发送问题")
            logger.debug(f"问题内容: {prompt[:200]}...")  # 只记录前200个字符

            # 获取指定的智能体
            agents = self._rag_client.list_agents(id=agent_id)
            if not agents:
                raise Exception(f"未找到智能体 ID: {agent_id}")

            agent = agents[0]
            logger.info(f"成功获取智能体: {getattr(agent, 'name', 'Unknown')}")

            # 创建会话
            session = agent.create_session()
            logger.info("成功创建会话")

            # 发送消息并获取响应
            response_content = ""
            logger.info("开始发送消息到智能体...")

            if stream:
                # 处理流式响应 - 根据参考代码修复
                logger.info("使用流式响应模式...")
                for ans in session.ask(prompt, stream=True):
                    # 流式响应中，ans.content 包含累积的完整内容
                    response_content = ans.content
                logger.info("流式响应完成")
            else:
                # 处理非流式响应
                logger.info("使用非流式响应模式...")
                response = session.ask(prompt, stream=False)
                if hasattr(response, 'content'):
                    response_content = response.content
                elif hasattr(response, 'answer'):
                    response_content = response.answer
                else:
                    response_content = str(response)
                logger.info("非流式响应完成")

            logger.info(f"智能体响应完成，内容长度: {len(response_content)}")
            return response_content

        except Exception as e:
            logger.error(f"RAGFlow 智能体调用失败: {str(e)}")
            raise

    def process_formatted_json(self, formatted_json: str) -> str:
        """
        处理格式化的JSON数据，发送给智能体进行分析

        Args:
            formatted_json: 格式化的策略JSON字符串

        Returns:
            str: 智能体的分析结果

        Raises:
            Exception: 当调用失败时抛出异常
        """
        if not formatted_json or not formatted_json.strip():
            raise Exception("输入的JSON数据为空")

        # 构造发送给智能体的提示词
        prompt = f"""{formatted_json}"""

        try:
            logger.info("开始处理格式化的JSON数据...")
            logger.info(f"JSON数据长度: {len(formatted_json)} 字符")

            # 调用智能体进行分析（使用流式响应，与参考代码一致）
            response = self.ask_agent(prompt, stream=True)

            logger.info("JSON数据处理完成")
            return response

        except Exception as e:
            logger.error(f"处理格式化JSON数据失败: {str(e)}")
            raise


# 创建默认服务实例
default_service = RAGFlowService()

# 创建专门用于策略分析的服务实例
policy_analysis_config = Config(
    api_key="ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
    base_url="http://*************:8011",
    assistant_id="cbaecd286f9211f0b4040242ac150003"
)
policy_analysis_service = RAGFlowService(policy_analysis_config)


# 模块级别的便捷函数
def process_formatted_json(formatted_json: str) -> str:
    """
    模块级别的便捷函数，用于处理格式化的JSON数据

    Args:
        formatted_json: 格式化的策略JSON字符串

    Returns:
        str: 智能体的分析结果

    Raises:
        Exception: 当调用失败时抛出异常
    """
    return policy_analysis_service.process_formatted_json(formatted_json)


class PolicyApplicationConfig:
    """策略申请分析配置类"""

    def __init__(self,
                 api_key: str = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
                 base_url: str = "http://*************:8011",
                 assistant_id: str = "3c3d4c94703911f091af0242ac150003"):
        """
        初始化策略申请分析配置

        Args:
            api_key: RAGFlow API密钥
            base_url: RAGFlow服务地址
            assistant_id: 策略申请分析智能体ID
        """
        self.RAGFLOW_API_KEY = api_key
        self.RAGFLOW_BASE_URL = base_url
        self.RAGFLOW_ASSISTANT_ID = assistant_id


class PolicyApplicationAnalysisService:
    """策略申请分析服务"""

    def __init__(self, config: Optional[PolicyApplicationConfig] = None):
        """
        初始化策略申请分析服务

        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or PolicyApplicationConfig()
        self._rag_client = None

        if RAGFLOW_AVAILABLE:
            try:
                self._rag_client = RAGFlow(
                    api_key=self.config.RAGFLOW_API_KEY,
                    base_url=self.config.RAGFLOW_BASE_URL
                )
                logger.info("策略申请分析RAGFlow客户端初始化成功")
            except Exception as e:
                logger.error(f"策略申请分析RAGFlow客户端初始化失败: {str(e)}")
                self._rag_client = None
        else:
            logger.warning("RAGFlow SDK 不可用，策略申请分析功能将不可用")

    def analyze_policy_application(self, key_value_details: str, stream: bool = True) -> str:
        """
        分析策略申请的键值对信息

        Args:
            key_value_details: 键值对详情字符串
            stream: 是否使用流式响应

        Returns:
            str: 智能体分析结果
        """
        if not RAGFLOW_AVAILABLE:
            raise Exception("RAGFlow SDK 不可用，无法进行策略申请分析")

        if not self._rag_client:
            raise Exception("策略申请分析客户端未初始化")

        try:
            # 构造分析提示词
            prompt = f"""
请分析以下策略申请的键值对信息，从安全角度给出专业建议：

{key_value_details}

请从以下几个方面进行分析：
1. 申请的合理性评估
2. 潜在的安全风险
3. 建议的安全措施
4. 是否需要额外的审批流程

请给出简洁明确的分析结果。
"""

            logger.info("开始策略申请分析...")
            logger.info(f"使用智能体ID: {self.config.RAGFLOW_ASSISTANT_ID}")
            logger.info(f"流式响应: {stream}")

            # 使用新的RAGFlow SDK API - 创建聊天会话
            import time
            chat_name = f"策略申请分析_{int(time.time())}"
            chat = self._rag_client.create_chat(name=chat_name)
            logger.info(f"成功创建聊天会话: {chat.id}")

            # 创建会话
            session = chat.create_session()
            logger.info("成功创建会话")

            # 发送消息并获取响应
            response_content = ""

            if stream:
                logger.info("使用流式响应模式...")
                for ans in session.ask(prompt, stream=True):
                    # 流式响应中，ans.content 包含累积的完整内容
                    response_content = ans.content
                logger.info("流式响应完成")
            else:
                logger.info("使用非流式响应模式...")
                ans = session.ask(prompt, stream=False)
                response_content = ans.content
                logger.info("非流式响应完成")

            logger.info(f"策略申请分析完成，响应长度: {len(response_content)} 字符")
            return response_content

        except Exception as e:
            error_msg = f"策略申请分析失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)


# 创建策略申请分析服务实例
policy_application_analysis_service = PolicyApplicationAnalysisService()


def analyze_policy_application_data(key_value_details: str) -> str:
    """
    分析策略申请数据的便捷函数

    Args:
        key_value_details: 键值对详情字符串

    Returns:
        str: 智能体的分析结果

    Raises:
        Exception: 当调用失败时抛出异常
    """
    return policy_application_analysis_service.analyze_policy_application(key_value_details)

