#!/usr/bin/env python3
"""
测试重构后的代码是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_policy_analysis_service():
    """测试策略分析服务"""
    try:
        from services.policy_analysis_service import (
            get_policy_risk_analysis,
            process_policy_data_with_objects,
            collect_time_format_error_details,
            analyze_dangerous_ports,
            process_form_data,
            validate_policy_time_format
        )
        print("✅ 策略分析服务模块导入成功")
        
        # 测试策略时间格式验证
        result = validate_policy_time_format("测试策略_20241201_30天_张三")
        print(f"✅ 策略时间格式验证功能正常: {result.get('has_time_info', False)}")
        
        return True
    except Exception as e:
        print(f"❌ 策略分析服务测试失败: {str(e)}")
        return False

def test_policy_time_service():
    """测试策略时间服务"""
    try:
        from services.policy_time_service import (
            check_policy_time_validity,
            print_time_validity_summary,
            print_all_policies_time_info,
            print_policies_time_table
        )
        print("✅ 策略时间服务模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 策略时间服务测试失败: {str(e)}")
        return False

def test_report_service():
    """测试报告服务"""
    try:
        from services.report_service import (
            print_expired_policies_details,
            print_expiring_soon_policies_details,
            print_invalid_time_policies_details,
            print_time_format_error_details
        )
        print("✅ 报告服务模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 报告服务测试失败: {str(e)}")
        return False

def test_form_router():
    """测试表单路由"""
    try:
        # 不直接导入路由，因为可能有依赖问题
        # 只检查文件是否存在且语法正确
        import ast
        with open('routers/form_router.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查语法
        ast.parse(content)
        print("✅ 表单路由文件语法正确")
        
        # 检查是否包含必要的路由函数
        if 'handle_form_validation' in content and 'get_risk_analysis' in content:
            print("✅ 表单路由包含必要的路由函数")
            return True
        else:
            print("❌ 表单路由缺少必要的路由函数")
            return False
            
    except Exception as e:
        print(f"❌ 表单路由测试失败: {str(e)}")
        return False

def test_integration():
    """测试集成功能"""
    try:
        from models.policy_object import PolicyObject, PolicyObjectList
        from services.policy_analysis_service import get_policy_risk_analysis
        
        # 创建测试数据
        test_policy_data = [
            {
                "id": "test_001",
                "name": "测试策略_20241201_30天_张三",
                "source": "192.168.1.0/24",
                "destination": "10.0.0.0/8",
                "service": "tcp/80,443",
                "action": "permit"
            }
        ]
        
        # 测试风险分析
        result = get_policy_risk_analysis(test_policy_data)
        
        if 'statistics' in result:
            print("✅ 集成测试成功 - 风险分析功能正常")
            print(f"   测试结果: 总策略数 {result['statistics']['total_policies']}")
            return True
        else:
            print("❌ 集成测试失败 - 风险分析结果格式错误")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔄 开始测试重构后的代码...")
    print("=" * 60)
    
    tests = [
        ("策略分析服务", test_policy_analysis_service),
        ("策略时间服务", test_policy_time_service),
        ("报告服务", test_report_service),
        ("表单路由", test_form_router),
        ("集成功能", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"   ⚠️ {test_name} 测试未通过")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        print("\n✅ 重构总结:")
        print("   - 策略分析功能已移动到 services/policy_analysis_service.py")
        print("   - 策略时间功能已移动到 services/policy_time_service.py")
        print("   - 报告打印功能已移动到 services/report_service.py")
        print("   - form_router.py 现在只包含路由函数")
        print("   - 所有原有功能保持不变")
    else:
        print("⚠️ 部分测试未通过，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    main()
