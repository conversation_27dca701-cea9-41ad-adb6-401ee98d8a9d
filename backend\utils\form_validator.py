"""
表单验证工具模块
"""

from typing import List, Dict
from models.form_models import KeyValueItem
from config import config

# 尝试导入RAGFlow SDK，如果不可用则设为None
try:
    from ragflow_sdk import RAGFlow
    RAGFLOW_AVAILABLE = True
except ImportError:
    RAGFlow = None
    RAGFLOW_AVAILABLE = False
    print("RAGFlow SDK 未安装，表单验证功能将使用简化模式")


def validate_form_data(items: List[KeyValueItem]) -> Dict:
    """
    表单校验逻辑
    支持以下校验规则：
    1. 年龄必须为数字
    2. 邮箱必须符合格式
    3. 手机号必须为11位数字
    4. 必填字段非空校验
    """
    # 初始化RAGFlow对象（如果SDK可用）
    rag_object = None
    if RAGFLOW_AVAILABLE and RAGFlow:
        try:
            rag_object = RAGFlow(api_key=config.RAGFLOW_API_KEY, base_url=config.RAGFLOW_BASE_URL)
        except Exception as e:
            print(f"RAGFlow初始化失败: {str(e)}")
            rag_object = None
    agent = rag_object.list_agents(id=config.FORM_VALIDATION_AGENT_ID)[0]
    session = agent.create_session()

    print("\n==============\n")
    print("Hello. What can I do for you?")

    # question = input("\n===== User ====\n> ")
    question = str(items)
    print(items)
    print("\n==== Content checking in progress ====\n")

    suggestion = []
    cont = ""
    for ans in session.ask(question, stream=True):
        print(ans.content[len(cont):], end='', flush=True)
        cont = ans.content
    suggestion.append(cont)
    print("\n==================\n")
    return {"suggestions": suggestion}
