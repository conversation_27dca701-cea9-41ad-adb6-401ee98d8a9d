"""
测试大模型检测API
验证新创建的大模型检测接口是否正常工作
"""

import requests
import json


def test_llm_detection_api():
    """测试大模型检测API"""
    
    print("🧪 测试大模型检测API")
    print("=" * 80)
    
    # 测试数据
    test_data = {
        "items": [
            {"key": "申请人", "value": "张三"},
            {"key": "部门", "value": "IT部"},
            {"key": "申请时间", "value": "2024-08-03"},
            {"key": "申请原因", "value": "业务需要开通防火墙端口"},
            {"key": "源地址", "value": "192.168.1.100"},
            {"key": "目标地址", "value": "10.0.0.50"},
            {"key": "端口", "value": "8080"},
            {"key": "协议", "value": "TCP"}
        ]
    }
    
    print(f"📊 测试数据:")
    print(f"  - 键值对数量: {len(test_data['items'])}")
    for i, item in enumerate(test_data['items'], 1):
        print(f"  {i:2d}. {item['key']} = {item['value']}")
    
    try:
        # 发送请求
        url = "http://127.0.0.1:8000/llm-detection/detect"
        headers = {"Content-Type": "application/json"}
        
        print(f"\n🔄 发送请求到: {url}")
        response = requests.post(url, json=test_data, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保后端服务已启动 (python -m uvicorn main:app --reload)")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_health_endpoint():
    """测试健康检查端点"""
    
    print(f"\n" + "="*80)
    print("🔍 测试健康检查端点")
    print("="*80)
    
    try:
        url = "http://127.0.0.1:8000/llm-detection/health"
        response = requests.get(url, timeout=5)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 健康检查成功!")
            print(f"📋 服务状态: {result.get('status', 'N/A')}")
            print(f"📋 服务名称: {result.get('service', 'N/A')}")
            print(f"📋 消息: {result.get('message', 'N/A')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保后端服务已启动")
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")


def main():
    """主测试函数"""
    
    print("🚀 开始大模型检测API测试")
    
    # 测试健康检查
    test_health_endpoint()
    
    # 测试主要功能
    test_llm_detection_api()
    
    print(f"\n" + "="*80)
    print("📊 测试总结")
    print("="*80)
    print("✅ 大模型检测API测试完成")
    print("\n📋 功能说明:")
    print("  1. 接收前端发送的键值对数据")
    print("  2. 验证和过滤数据")
    print("  3. 返回检测结果（目前是模拟数据）")
    print("\n🚀 启动后端服务命令:")
    print("  cd backend")
    print("  python -m uvicorn main:app --reload")
    print("\n🌐 API端点:")
    print("  - POST /llm-detection/detect - 大模型检测")
    print("  - GET /llm-detection/health - 健康检查")


if __name__ == "__main__":
    main()
