"""
测试原有的RAGFlow服务
"""

def test_original_ragflow():
    """测试原有的RAGFlow服务"""
    
    print("🧪 测试原有的RAGFlow服务")
    print("=" * 80)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from services.ragflow_service import process_formatted_json
        
        # 测试数据
        test_json = '''
 {"id": "001","策略名称": "测试策略-1234-长期","源地址": ["192.168.1.0/24"],"目的地址": ["any"]},
;
'''
        
        print(f"📊 测试数据:")
        print(test_json)
        
        print(f"\n🔄 开始调用原有RAGFlow服务...")
        
        result = process_formatted_json(test_json)
        
        print(f"✅ 原有RAGFlow服务成功!")
        print(f"📋 分析结果长度: {len(result)} 字符")
        print(f"📋 分析结果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ 原有RAGFlow服务失败: {str(e)}")
        return False


if __name__ == "__main__":
    test_original_ragflow()
