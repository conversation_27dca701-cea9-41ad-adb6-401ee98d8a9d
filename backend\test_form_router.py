"""
测试form_router的危险端口检测功能
"""

import asyncio
from routers.form_router import handle_form_validation


async def test_dangerous_port_detection():
    """测试危险端口检测功能"""
    
    # 模拟前端发送的策略数据
    test_request = {
        "type": "policy_data",
        "count": 5,
        "data": [
            {
                "id": "001",
                "name": "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
                "src_zone": "internal",
                "src_addr": ["192.168.1.0/24"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["TCP 21", "TCP 23", "UDP 53"],  # FTP, Telnet, DNS
                "action": "允许",
                "hit_count": "1500"
            },
            {
                "id": "002", 
                "name": "基础策略-主动防御封禁-IN",
                "src_zone": "any",
                "src_addr": ["any"],
                "dst_zone": "internal",
                "dst_addr": ["10.0.0.0/8"],
                "service": ["TCP 0", "TCP 1", "UDP 31337"],  # 高危端口
                "action": "拒绝",
                "hit_count": "50000"
            },
            {
                "id": "003",
                "name": "240816-省内地址-产业园微碳慧能主题网站",
                "src_zone": "dmz",
                "src_addr": ["172.16.0.0/16"],
                "dst_zone": "external", 
                "dst_addr": ["any"],
                "service": ["TCP 443", "TCP 80", "UDP 12345"],  # 相对安全的端口
                "action": "允许",
                "hit_count": "2000"
            },
            {
                "id": "004",
                "name": "应急指挥系统基准策略勿动-20240507-长期",
                "src_zone": "management",
                "src_addr": ["10.10.10.0/24"],
                "dst_zone": "any",
                "dst_addr": ["any"],
                "service": ["UDP 50000-60000", "TCP 1-100"],  # 大范围端口
                "action": "允许",
                "hit_count": "800"
            },
            {
                "id": "005",
                "name": "20231122-省侧work节点服务-边缘云-ldh-长期",
                "src_zone": "cloud",
                "src_addr": ["203.0.113.0/24"],
                "dst_zone": "internal",
                "dst_addr": ["192.168.0.0/16"],
                "service": ["ICMP", "any", "TCP 8080"],  # 混合服务
                "action": "允许",
                "hit_count": "300"
            }
        ]
    }
    
    print("🧪 测试危险端口检测功能")
    print("=" * 60)
    
    # 调用处理函数
    try:
        result = await handle_form_validation(test_request)
        print(f"\n✅ 处理完成，返回结果: {result}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_dangerous_port_detection())
