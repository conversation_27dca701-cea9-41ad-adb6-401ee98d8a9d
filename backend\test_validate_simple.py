"""
简化测试 - 直接测试数据解析和JSON构造逻辑
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入需要的模块
from models.policy_object import PolicyObject


def test_json_construction():
    """测试JSON构造逻辑"""
    
    print("🧪 测试数据解析和JSON构造功能")
    print("=" * 80)
    
    # 模拟策略数据
    policy_data = [
        {
            "id": "001",
            "name": "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
            "src_zone": "internal",
            "src_addr": ["192.168.1.0/24", "192.168.2.0/24"],
            "dst_zone": "external",
            "dst_addr": ["any"],
            "service": ["TCP 80", "TCP 443"],
            "action": "允许",
            "hit_count": "1500"
        },
        {
            "id": "002", 
            "name": "基础策略-主动防御封禁-IN",
            "src_zone": "any",
            "src_addr": ["any"],
            "dst_zone": "internal",
            "dst_addr": ["10.0.0.0/8"],
            "service": ["TCP 135", "TCP 139"],
            "action": "拒绝",
            "hit_count": "50000"
        },
        {
            "id": "003",
            "name": "240816-省内地址-产业园微碳慧能主题网站",
            "src_zone": "dmz",
            "src_addr": ["172.16.0.0/16"],
            "dst_zone": "external", 
            "dst_addr": ["203.0.113.0/24", "198.51.100.0/24"],
            "service": ["TCP 443", "TCP 80"],
            "action": "允许",
            "hit_count": "2000"
        },
        {
            "id": "004",
            "name": "应急指挥系统基准策略勿动-20240507-长期",
            "src_zone": "management",
            "src_addr": ["10.10.10.0/24"],
            "dst_zone": "any",
            "dst_addr": ["any"],
            "service": ["UDP 53", "TCP 22"],
            "action": "允许",
            "hit_count": "800"
        },
        {
            "id": "005",
            "name": "20231122-省侧work节点服务-边缘云-ldh-长期",
            "src_zone": "cloud",
            "src_addr": ["203.0.113.0/24"],
            "dst_zone": "internal",
            "dst_addr": ["192.168.0.0/16"],
            "service": ["ICMP", "TCP 8080"],
            "action": "允许",
            "hit_count": "300"
        },
        {
            "id": "006",
            "name": "测试策略-办公网络访问",
            "src_zone": "office",
            "src_addr": ["192.168.100.0/24"],
            "dst_zone": "internet",
            "dst_addr": ["any"],
            "service": ["TCP 80", "TCP 443", "UDP 53"],
            "action": "允许",
            "hit_count": "1200"
        },
        {
            "id": "007",
            "name": "数据库访问策略-生产环境",
            "src_zone": "app_server",
            "src_addr": ["10.1.1.0/24"],
            "dst_zone": "database",
            "dst_addr": ["10.2.1.10", "10.2.1.11"],
            "service": ["TCP 3306", "TCP 5432"],
            "action": "允许",
            "hit_count": "5000"
        },
        {
            "id": "008",
            "name": "监控系统策略-SNMP访问",
            "src_zone": "monitor",
            "src_addr": ["10.3.1.100"],
            "dst_zone": "network_device",
            "dst_addr": ["10.4.0.0/16"],
            "service": ["UDP 161", "UDP 162"],
            "action": "允许",
            "hit_count": "800"
        },
        {
            "id": "009",
            "name": "邮件服务器策略-SMTP访问",
            "src_zone": "mail_server",
            "src_addr": ["10.5.1.10"],
            "dst_zone": "external",
            "dst_addr": ["any"],
            "service": ["TCP 25", "TCP 587", "TCP 465"],
            "action": "允许",
            "hit_count": "2500"
        },
        {
            "id": "010",
            "name": "VPN访问策略-远程办公",
            "src_zone": "vpn_client",
            "src_addr": ["192.168.200.0/24"],
            "dst_zone": "internal",
            "dst_addr": ["10.0.0.0/8"],
            "service": ["any"],
            "action": "允许",
            "hit_count": "1800"
        },
        {
            "id": "011",
            "name": "备份系统策略-数据同步",
            "src_zone": "backup_server",
            "src_addr": ["10.6.1.20"],
            "dst_zone": "storage",
            "dst_addr": ["10.7.1.0/24"],
            "service": ["TCP 22", "TCP 873"],
            "action": "允许",
            "hit_count": "600"
        },
        {
            "id": "012",
            "name": "安全扫描策略-漏洞检测",
            "src_zone": "security_scanner",
            "src_addr": ["10.8.1.50"],
            "dst_zone": "all_zones",
            "dst_addr": ["any"],
            "service": ["TCP 1-65535"],
            "action": "允许",
            "hit_count": "10000"
        }
    ]
    
    print(f"📊 测试数据: {len(policy_data)} 个策略")
    print()
    
    try:
        # 1. 将前端发来的数据解析为对象数组
        policy_objects = []
        for item in policy_data:
            try:
                policy_obj = PolicyObject.from_dict(item)
                policy_objects.append(policy_obj)
            except Exception as e:
                print(f"⚠️ 解析策略对象失败: {str(e)}, 数据: {item}")
                continue
        
        print(f"✅ 成功解析 {len(policy_objects)} 个策略对象")
        
        # 2. 构造JSON，修改键名为中文，只包含id、策略名称、源地址、目的地址这4个字段
        json_objects = []
        for policy_obj in policy_objects:
            json_obj = {
                "id": policy_obj.id,
                "策略名称": policy_obj.name,
                "源地址": policy_obj.src_addr,
                "目的地址": policy_obj.dst_addr
            }
            json_objects.append(json_obj)

        # 3. 每5个JSON对象之间加个英文分号，不使用中括号包裹
        json_parts = []
        for i in range(0, len(json_objects), 5):
            batch = json_objects[i:i+5]
            # 将每个对象转换为单行JSON字符串
            batch_lines = []
            for obj in batch:
                obj_json = json.dumps(obj, ensure_ascii=False, separators=(',', ': '))
                batch_lines.append(f" {obj_json}")

            # 用逗号和换行连接同一批次的对象
            batch_str = ",\n".join(batch_lines)
            json_parts.append(batch_str)

        # 4. 将最终构造的JSON打印出来，每5个对象后加分号
        final_json = ",\n;\n".join(json_parts)

        # 如果有数据，在最后添加分号
        if json_parts:
            final_json += ",\n;"
        print(f"\n📋 构造的JSON结果:")
        print("=" * 80)
        print(final_json)
        print("=" * 80)
        
        print(f"\n✅ 测试完成!")
        print(f"  - 总策略数: {len(policy_objects)}")
        print(f"  - JSON分组数: {len(json_parts)}")
        print(f"  - 每组最多5个策略")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_json_construction()
