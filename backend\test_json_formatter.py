"""
测试JSON格式化工具类
验证移植后的功能是否正常
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.json_formatter import PolicyJsonFormatter


def test_json_formatter():
    """测试JSON格式化工具类"""
    
    print("🧪 测试JSON格式化工具类")
    print("=" * 80)
    
    # 模拟策略数据
    policy_data = [
        {
            "id": "164",
            "name": "240409-常州后勤管理系统运维终端-后勤管理系统运维端口-杨浩然-长期",
            "src_zone": "internal",
            "src_addr": ["常州后勤管理系统运维终端"],
            "dst_zone": "external",
            "dst_addr": ["后勤管理系统运维端口"],
            "service": ["TCP 80", "TCP 443"],
            "action": "允许",
            "hit_count": "1500"
        },
        {
            "id": "176",
            "name": "240415-盐城市公司地址-后勤管理系统业务端口-杨浩然-长期",
            "src_zone": "any",
            "src_addr": ["盐城IP地址"],
            "dst_zone": "internal",
            "dst_addr": ["后勤管理系统运维终端"],
            "service": ["TCP 135", "TCP 139"],
            "action": "拒绝",
            "hit_count": "50000"
        },
        {
            "id": "201",
            "name": "240501-南京办公终端-财务系统接口-李明-长期",
            "src_zone": "dmz",
            "src_addr": ["南京总部5楼办公终端"],
            "dst_zone": "external", 
            "dst_addr": ["财务系统数据接口"],
            "service": ["TCP 443", "TCP 80"],
            "action": "允许",
            "hit_count": "2000"
        },
        {
            "id": "202",
            "name": "240502-苏州服务器集群-ERP系统服务-张伟-短期",
            "src_zone": "management",
            "src_addr": ["苏州园区服务器"],
            "dst_zone": "any",
            "dst_addr": ["ERP系统核心服务"],
            "service": ["UDP 53", "TCP 22"],
            "action": "允许",
            "hit_count": "800"
        },
        {
            "id": "203",
            "name": "240503-无锡物联终端-智能仓储模块-王芳-长期",
            "src_zone": "cloud",
            "src_addr": ["无锡物流中心物联终端"],
            "dst_zone": "internal",
            "dst_addr": ["智能仓储管理系统"],
            "service": ["ICMP", "TCP 8080"],
            "action": "允许",
            "hit_count": "300"
        },
        {
            "id": "204",
            "name": "240504-常州研发终端-研发管理系统-赵强-长期",
            "src_zone": "office",
            "src_addr": ["常州科技园3楼研发终端"],
            "dst_zone": "internet",
            "dst_addr": ["研发管理数据库"],
            "service": ["TCP 80", "TCP 443", "UDP 53"],
            "action": "允许",
            "hit_count": "1200"
        },
        {
            "id": "205",
            "name": "240505-盐城办公终端-HR系统接口-杨浩然-短期",
            "src_zone": "app_server",
            "src_addr": ["盐城公司2楼办公终端"],
            "dst_zone": "database",
            "dst_addr": ["HR系统服务接口"],
            "service": ["TCP 3306", "TCP 5432"],
            "action": "允许",
            "hit_count": "5000"
        },
        {
            "id": "206",
            "name": "240506-南通服务器-物流系统端口-李娜-长期",
            "src_zone": "monitor",
            "src_addr": ["南通数据中心服务器"],
            "dst_zone": "network_device",
            "dst_addr": ["物流系统业务端口"],
            "service": ["UDP 161", "UDP 162"],
            "action": "允许",
            "hit_count": "800"
        },
        {
            "id": "207",
            "name": "240507-徐州办公终端-采购系统-周杰-长期",
            "src_zone": "mail_server",
            "src_addr": ["徐州办公楼4楼终端"],
            "dst_zone": "external",
            "dst_addr": ["采购管理系统"],
            "service": ["TCP 25", "TCP 587", "TCP 465"],
            "action": "允许",
            "hit_count": "2500"
        },
        {
            "id": "208",
            "name": "240508-淮安物联终端-监控系统服务-刘洋-短期",
            "src_zone": "vpn_client",
            "src_addr": ["淮安工厂物联终端"],
            "dst_zone": "internal",
            "dst_addr": ["监控系统数据服务"],
            "service": ["any"],
            "action": "允许",
            "hit_count": "1800"
        }
    ]
    
    print(f"📊 测试数据: {len(policy_data)} 个策略")
    print()
    
    try:
        # 测试解析和格式化功能
        policy_objects, formatted_json = PolicyJsonFormatter.parse_and_format_policy_data(policy_data)
        
        # 打印格式化的JSON结果
        PolicyJsonFormatter.print_formatted_json(formatted_json, "移植后的JSON格式化结果")
        
        print(f"\n✅ 测试完成!")
        print(f"  - 总策略数: {len(policy_objects)}")
        print(f"  - JSON格式: 符合要求的格式")
        print(f"  - 功能状态: 移植成功，功能正常")
        
        # 验证格式是否符合要求
        lines = formatted_json.split('\n')
        semicolon_count = formatted_json.count(';')
        print(f"  - 分号数量: {semicolon_count}")
        print(f"  - 总行数: {len([line for line in lines if line.strip()])}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_json_formatter()
