from datetime import datetime

class AttackRecord:
    def __init__(self):
        self.attack_time: datetime = None  # 攻击时间
        self.mark: str = None  # 标记
        self.source_ip: str = None  # 源IP
        self.source_port: int = None  # 源端口
        self.direction: str = None  # 方向
        self.dest_ip: str = None  # 目的IP
        self.dest_port: int = None  # 目的端口
        self.app_protocol: str = None  # 应用协议
        self.session_count: int = None  # 会话总数
        self.attack_type: str = None  # 攻击类型
        self.attack_status: str = None  # 攻击状态
        self.attack_level: str = None  # 攻击级别
        self.external_info: str = None  # 外部信息
        self.pcap_size: str = None  # pcap包大小
        self.pcap_content: str = None  # pcap包内容

    def __str__(self):
        return (f"AttackRecord(attack_time={self.attack_time}, "
                f"source_ip={self.source_ip}, source_port={self.source_port}, "
                f"direction={self.direction}, dest_ip={self.dest_ip}, "
                f"dest_port={self.dest_port}, app_protocol={self.app_protocol}, "
                f"attack_type={self.attack_type}, attack_status={self.attack_status}, "
                f"attack_level={self.attack_level})")

    def get_unique_key(self) -> str:
        """
        获取用于判重的唯一键，忽略源端口
        :return: 源IP_目的IP_目的端口 组成的字符串
        """
        return f"{self.source_ip}_{self.dest_ip}_{self.dest_port}"
        # return f"{self.dest_ip}_{self.dest_port}"

    def __eq__(self, other):
        """
        重写相等性判断方法
        :param other: 另一个 AttackRecord 对象
        :return: 如果源IP、目的IP和目的端口都相同，则返回True（忽略源端口）
        """
        if not isinstance(other, AttackRecord):
            return False
        return self.get_unique_key() == other.get_unique_key()

    def __hash__(self):
        """
        重写哈希方法
        :return: 唯一键的哈希值
        """
        return hash(self.get_unique_key())

    @staticmethod
    def from_dict(data: dict) -> 'AttackRecord':
        """
        从字典创建 AttackRecord 实例
        """
        record = AttackRecord()
        if 'attack_time' in data:
            try:
                if isinstance(data['attack_time'], str):
                    record.attack_time = datetime.strptime(data['attack_time'], '%Y/%m/%d %H:%M')
                else:
                    record.attack_time = data['attack_time']
            except ValueError:
                record.attack_time = data['attack_time']  # 保持原始值
                
        record.mark = data.get('mark')
        record.source_ip = data.get('source_ip')
        record.source_port = int(data['source_port']) if data.get('source_port') else None
        record.direction = data.get('direction')
        record.dest_ip = data.get('dest_ip')
        record.dest_port = int(data['dest_port']) if data.get('dest_port') else None
        record.app_protocol = data.get('app_protocol')
        record.session_count = int(data['session_count']) if data.get('session_count') else None
        record.attack_type = data.get('attack_type')
        record.attack_status = data.get('attack_status')
        record.attack_level = data.get('attack_level')
        record.external_info = data.get('external_info')
        record.pcap_size = data.get('pcap_size')
        record.pcap_content = data.get('pcap_content')
        return record 