"""
测试策略申请检测API
验证端口风险检测功能
"""

import requests
import json


def test_policy_application_api():
    """测试策略申请检测API"""
    
    print("🧪 测试策略申请检测API")
    print("=" * 80)
    
    # 测试用例
    test_cases = [
        {
            "name": "包含高风险端口的申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "张三"},
                    {"key": "部门", "value": "IT部"},
                    {"key": "目的端口", "value": "1234"},  # 高风险端口
                    {"key": "协议", "value": "TCP"},
                    {"key": "申请原因", "value": "业务需要"}
                ]
            }
        },
        {
            "name": "包含多个端口的申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "李四"},
                    {"key": "目的端口", "value": "80、443、8080"},  # 多个端口
                    {"key": "协议", "value": "TCP"},
                    {"key": "申请原因", "value": "Web服务"}
                ]
            }
        },
        {
            "name": "端口范围申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "王五"},
                    {"key": "目的端口", "value": "8000-8010"},  # 端口范围
                    {"key": "协议", "value": "TCP"},
                    {"key": "申请原因", "value": "应用服务"}
                ]
            }
        },
        {
            "name": "any端口申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "赵六"},
                    {"key": "目的端口", "value": "any"},  # any端口
                    {"key": "协议", "value": "TCP"},
                    {"key": "申请原因", "value": "全端口访问"}
                ]
            }
        },
        {
            "name": "安全端口申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "孙七"},
                    {"key": "目的端口", "value": "80"},  # 相对安全的端口
                    {"key": "协议", "value": "TCP"},
                    {"key": "申请原因", "value": "HTTP服务"}
                ]
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 60)
        
        test_data = test_case['data']
        print(f"📊 测试数据:")
        for item in test_data['items']:
            print(f"  - {item['key']}: {item['value']}")
        
        try:
            # 发送请求
            url = "http://127.0.0.1:8000/policy-application/detect"
            headers = {"Content-Type": "application/json"}
            
            response = requests.post(url, json=test_data, headers=headers, timeout=10)
            
            print(f"\n📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求成功!")
                
                detection_data = result.get('data', {})
                print(f"📋 检测结果:")
                print(f"  - 处理状态: {detection_data.get('status', 'N/A')}")
                print(f"  - 处理数量: {detection_data.get('items_processed', 0)}")
                print(f"  - 是否有风险: {detection_data.get('has_risks', False)}")
                
                # 显示提取的字段
                extracted = detection_data.get('extracted_fields', {})
                print(f"  - 提取的端口: {extracted.get('destination_port', '未找到')}")
                print(f"  - 提取的协议: {extracted.get('protocol', '未找到')}")
                
                # 显示风险详情
                risks = detection_data.get('port_risks', [])
                if risks:
                    print(f"  - 发现 {len(risks)} 个风险:")
                    for j, risk in enumerate(risks, 1):
                        print(f"    {j}. {risk['protocol']}:{risk['port']} - {risk['risk_level']} - {risk['reason']}")
                else:
                    print(f"  - 未发现端口风险")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败: 请确保后端服务已启动")
            break
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")


def test_health_endpoint():
    """测试健康检查端点"""
    
    print(f"\n" + "="*80)
    print("🔍 测试健康检查端点")
    print("="*80)
    
    try:
        url = "http://127.0.0.1:8000/policy-application/health"
        response = requests.get(url, timeout=5)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 健康检查成功!")
            print(f"📋 服务状态: {result.get('status', 'N/A')}")
            print(f"📋 服务名称: {result.get('service', 'N/A')}")
            print(f"📋 消息: {result.get('message', 'N/A')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保后端服务已启动")
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")


def test_port_parsing():
    """测试端口解析功能"""
    
    print(f"\n" + "="*80)
    print("🔧 测试端口解析功能")
    print("="*80)
    
    # 导入端口解析函数进行单元测试
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from routers.policy_application_router import parse_ports
        
        test_cases = [
            ("80", {80}),
            ("80、443、8080", {80, 443, 8080}),
            ("8000-8005", {8000, 8001, 8002, 8003, 8004, 8005}),
            ("any", set()),
            ("80,443", {80, 443}),
            ("invalid", set()),
        ]
        
        print("📋 端口解析测试:")
        for input_str, expected in test_cases:
            result = parse_ports(input_str)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{input_str}' -> {result}")
            
    except ImportError as e:
        print(f"❌ 无法导入端口解析函数: {str(e)}")


def main():
    """主测试函数"""
    
    print("🚀 开始策略申请检测API测试")
    
    # 测试健康检查
    test_health_endpoint()
    
    # 测试端口解析
    test_port_parsing()
    
    # 测试主要功能
    test_policy_application_api()
    
    print(f"\n" + "="*80)
    print("📊 测试总结")
    print("="*80)
    print("✅ 策略申请检测API测试完成")
    print("\n📋 功能说明:")
    print("  1. 接收策略申请键值对数据")
    print("  2. 提取目的端口和协议字段")
    print("  3. 解析各种端口格式（单个、多个、范围、any）")
    print("  4. 查询端口风险映射表")
    print("  5. 返回风险检测结果")
    print("\n🚀 启动后端服务命令:")
    print("  cd backend")
    print("  python -m uvicorn main:app --reload")
    print("\n🌐 API端点:")
    print("  - POST /policy-application/detect - 策略申请检测")
    print("  - GET /policy-application/health - 健康检查")


if __name__ == "__main__":
    main()
