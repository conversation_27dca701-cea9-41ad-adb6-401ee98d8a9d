import pandas as pd
import time
from datetime import datetime, timedelta
from typing import Optional, Generator, Set
import logging
from pathlib import Path
import json
from backend.pojo.attack_record import AttackRecord

# 配置日志
logging.basicConfig(
    level=logging.ERROR,  # 只显示错误日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TimeWindowProcessor:
    """时间窗口处理器"""
    
    def __init__(self, window_size: int = 5):
        """
        初始化时间窗口处理器
        :param window_size: 时间窗口大小（秒）
        """
        self.window_size = window_size
        self.records: Set[AttackRecord] = set()  # 存储时间窗口内的记录
        self.last_attack_time: Optional[datetime] = None  # 上一次攻击时间
        
    def process_record(self, record: AttackRecord) -> Optional[Set[AttackRecord]]:
        """
        处理一条攻击记录
        :param record: 攻击记录
        :return: 如果时间窗口结束，返回集合中的所有记录；否则返回None
        """
        current_time = record.attack_time
        
        # 如果是第一条记录
        if self.last_attack_time is None:
            self.last_attack_time = current_time
            self.records.add(record)
            return None
            
        # 计算时间差
        time_diff = current_time - self.last_attack_time
        
        # 如果超过时间窗口
        if time_diff.total_seconds() >= self.window_size:
            # 保存当前集合
            result_set = self.records.copy()
            # 清空集合并添加新记录
            self.records.clear()
            self.records.add(record)
            # 更新时间
            self.last_attack_time = current_time
            return result_set
        else:
            # 在时间窗口内，添加到集合
            self.records.add(record)
            return None

class ExcelReader:
    """Excel文件读取器，支持增量读取"""
    
    def __init__(self, file_path: str, chunk_size: int = 10):
        """
        初始化Excel读取器
        :param file_path: Excel文件路径
        :param chunk_size: 每次读取的行数
        """
        self.file_path = Path(file_path)
        self.chunk_size = chunk_size
        self._current_row = 0  # 当前读取位置
        
        if not self.file_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {file_path}")
            
        # 获取总行数和列名
        df_info = pd.read_excel(
            self.file_path,
            engine='openpyxl'
        )
        self._total_rows = len(df_info)
        
    def read_chunk(self) -> Generator[AttackRecord, None, None]:
        """
        读取一个数据块
        :return: 生成器，每次返回一条AttackRecord记录
        """
        try:
            # 读取指定行数的数据，跳过表头
            df_chunk = pd.read_excel(
                self.file_path,
                skiprows=range(1, self._current_row + 1),  # 跳过表头和已读取的行
                nrows=self.chunk_size,
                engine='openpyxl'
            )
            
            # 如果没有数据了，结束迭代
            if df_chunk.empty:
                return
                
            # 处理每一行数据
            for _, row in df_chunk.iterrows():
                try:
                    # 将DataFrame行转换为字典，使用iloc访问位置索引
                    row_dict = {
                        'attack_time': row.iloc[1] if not pd.isna(row.iloc[1]) else None,
                        'mark': row.iloc[2] if not pd.isna(row.iloc[2]) else None,
                        'source_ip': row.iloc[3] if not pd.isna(row.iloc[3]) else None,
                        'source_port': row.iloc[4] if not pd.isna(row.iloc[4]) else None,
                        'direction': row.iloc[5] if not pd.isna(row.iloc[5]) else None,
                        'dest_ip': row.iloc[6] if not pd.isna(row.iloc[6]) else None,
                        'dest_port': row.iloc[7] if not pd.isna(row.iloc[7]) else None,
                        'app_protocol': row.iloc[8] if not pd.isna(row.iloc[8]) else None,
                        'session_count': row.iloc[9] if not pd.isna(row.iloc[9]) else None,
                        'attack_type': row.iloc[10] if not pd.isna(row.iloc[10]) else None,
                        'attack_status': row.iloc[11] if not pd.isna(row.iloc[11]) else None,
                        'attack_level': row.iloc[12] if not pd.isna(row.iloc[12]) else None,
                        'external_info': row.iloc[13] if not pd.isna(row.iloc[13]) else None,
                        'pcap_size': row.iloc[14] if not pd.isna(row.iloc[14]) else None,
                        'pcap_content': row.iloc[15] if not pd.isna(row.iloc[15]) else None
                    }
                    
                    # 使用AttackRecord类的工厂方法创建实例
                    record = AttackRecord.from_dict(row_dict)
                    self._current_row += 1
                    yield record
                    
                except Exception as e:
                    logger.error(f"处理行 {self._current_row + 1} 时发生错误: {str(e)}")
                    raise
                
        except Exception as e:
            logger.error(f"读取Excel数据时发生错误: {str(e)}")
            raise
            
    @property
    def progress(self) -> float:
        """返回当前读取进度（百分比）"""
        return (self._current_row / self._total_rows) * 100 if self._total_rows > 0 else 0

class DataOutputHandler:
    """数据输出处理器"""
    
    def __init__(self, log_file: str = "attack_records.log"):
        """
        初始化输出处理器
        :param log_file: 日志文件路径
        """
        self.log_file = log_file
        # 检查文件是否存在，如果不存在则创建并写入表头
        if not Path(log_file).exists():
            self._write_header()
    
    def _write_header(self):
        """写入CSV格式的表头"""
        headers = [
            "记录时间", "攻击时间", "标记", "源IP", "源端口", "方向", 
            "目的IP", "目的端口", "应用协议", "会话总数", "攻击类型", 
            "攻击状态", "攻击级别", "外部信息", "pcap包大小"
        ]
        try:
            with open(self.log_file, 'w', encoding='utf-8', newline='') as f:
                f.write('\t'.join(headers) + '\n')
        except Exception as e:
            logger.error(f"写入表头时发生错误: {str(e)}")
    
    @staticmethod
    def output_to_console(record: AttackRecord):
        """
        将记录输出到控制台
        :param record: AttackRecord实例
        """
        print(f"\n{'='*50}")
        print(f"读取到新的攻击记录:")
        print(f"攻击时间: {record.attack_time}")
        print(f"源IP: {record.source_ip}:{record.source_port}")
        print(f"目的IP: {record.dest_ip}:{record.dest_port}")
        print(f"攻击类型: {record.attack_type}")
        print(f"攻击级别: {record.attack_level}")
        print(f"{'='*50}")
    
    def output_time_window_records(self, records: Set[AttackRecord]):
        """
        输出时间窗口内的所有记录到控制台和日志文件
        :param records: 时间窗口内的记录集合
        """
        # 控制台输出
        print(f"\n{'#'*70}")
        print(f"时间窗口结束，共有 {len(records)} 条记录:")
        
        try:
            # 写入日志文件
            with open(self.log_file, 'a', encoding='utf-8', newline='') as f:
                # 处理每条记录
                for record in sorted(records, key=lambda x: x.attack_time):
                    # 控制台输出
                    # print(f"\n{'-'*50}")
                    # print(f"攻击时间: {record.attack_time}")
                    # print(f"源IP: {record.source_ip}:{record.source_port}")
                    # print(f"目的IP: {record.dest_ip}:{record.dest_port}")
                    # print(f"攻击类型: {record.attack_type}")
                    # print(f"攻击级别: {record.attack_level}")
                    
                    # 准备记录行
                    row = [
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        record.attack_time.strftime('%Y-%m-%d %H:%M:%S') if record.attack_time else '',
                        record.mark or '',
                        record.source_ip or '',
                        str(record.source_port) if record.source_port else '',
                        record.direction or '',
                        record.dest_ip or '',
                        str(record.dest_port) if record.dest_port else '',
                        record.app_protocol or '',
                        str(record.session_count) if record.session_count else '',
                        record.attack_type or '',
                        record.attack_status or '',
                        record.attack_level or '',
                        record.external_info or '',
                        record.pcap_size or ''
                    ]
                    
                    # 写入一行记录，使用制表符分隔
                    f.write('\t'.join(row) + '\n')
                    
        except Exception as e:
            logger.error(f"写入日志文件时发生错误: {str(e)}")
        
        print(f"\n{'#'*70}")

def main():
    # 配置参数
    EXCEL_PATH = "example.xlsx"
    CHUNK_SIZE = 1  # 每次读取1行
    READ_INTERVAL = 0.01  # 每次读取间隔0.5秒
    WINDOW_SIZE = 1800  # 时间窗口大小（秒）
    LOG_FILE = "attack_records1.log"  # 日志文件路径
    
    try:
        # 初始化读取器和处理器
        reader = ExcelReader(EXCEL_PATH, CHUNK_SIZE)
        output_handler = DataOutputHandler(LOG_FILE)
        time_window_processor = TimeWindowProcessor(WINDOW_SIZE)
        
        while True:
            # 读取一块数据
            for record in reader.read_chunk():
                # 处理时间窗口
                window_records = time_window_processor.process_record(record)
                
                # 如果时间窗口结束，输出集合中的记录
                if window_records is not None:
                    output_handler.output_time_window_records(window_records)
                
                # 输出当前记录
                # output_handler.output_to_console(record)
                
            # 如果已经读完所有数据，输出最后一个时间窗口的记录
            if reader.progress >= 100:
                if time_window_processor.records:
                    output_handler.output_time_window_records(time_window_processor.records)
                break
                
            # 等待一段时间后继续读取
            time.sleep(READ_INTERVAL)
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()