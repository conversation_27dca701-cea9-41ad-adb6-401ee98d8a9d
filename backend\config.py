"""
配置模块
包含应用程序的所有配置项
"""

class Config:
    """应用程序配置类"""
    
    # UMI-OCR 配置
    UMI_OCR_API_URL = "http://localhost:1224/api/ocr"  # UMI-OCR API地址
    
    # RAGFlow 配置
    RAGFLOW_API_KEY = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm"  # RAGFlow API密钥
    RAGFLOW_BASE_URL = "http://*************:8011"  # RAGFlow API地址
    RAGFLOW_ASSISTANT_ID = "00c390745ed211f0bb210242ac150006"  # RAGFlow 智能体ID
    
    # 表单验证智能体配置
    FORM_VALIDATION_AGENT_ID = "815cced04b1811f0bfdf0242ac150006"  # 表单验证智能体ID
    
    # 文件配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}


# 全局配置实例
config = Config()
