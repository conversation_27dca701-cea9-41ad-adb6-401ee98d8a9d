"""
测试RAGFlow API
探索正确的API调用方式
"""

def test_ragflow_api():
    """测试RAGFlow API"""
    
    print("🧪 测试RAGFlow API")
    print("=" * 80)
    
    try:
        from ragflow_sdk import RAGFlow
        
        # 创建客户端
        client = RAGFlow(
            api_key="ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
            base_url="http://10.100.98.181:8011"
        )
        
        print("✅ RAGFlow客户端创建成功")
        
        # 探索可用的方法
        print("\n📋 RAGFlow客户端可用方法:")
        methods = [method for method in dir(client) if not method.startswith('_')]
        for method in methods:
            print(f"  - {method}")
        
        # 尝试不同的方法来获取智能体
        assistant_id = "3c3d4c94703911f091af0242ac150003"
        
        print(f"\n🔄 尝试获取智能体 ID: {assistant_id}")
        
        # 方法1: 尝试 list_agents
        if hasattr(client, 'list_agents'):
            try:
                agents = client.list_agents(id=assistant_id)
                print(f"✅ list_agents 成功: {len(agents)} 个智能体")
                if agents:
                    agent = agents[0]
                    print(f"  - 智能体名称: {getattr(agent, 'name', 'Unknown')}")
            except Exception as e:
                print(f"❌ list_agents 失败: {str(e)}")
        
        # 方法2: 尝试 get_assistant
        if hasattr(client, 'get_assistant'):
            try:
                assistant = client.get_assistant(assistant_id)
                print(f"✅ get_assistant 成功")
                print(f"  - 智能体名称: {getattr(assistant, 'name', 'Unknown')}")
            except Exception as e:
                print(f"❌ get_assistant 失败: {str(e)}")
        
        # 方法3: 尝试 assistants
        if hasattr(client, 'assistants'):
            try:
                assistants = client.assistants
                print(f"✅ assistants 属性存在")
                if hasattr(assistants, 'get'):
                    assistant = assistants.get(assistant_id)
                    print(f"✅ assistants.get 成功")
            except Exception as e:
                print(f"❌ assistants 失败: {str(e)}")
        
        # 方法4: 尝试其他可能的方法
        for method_name in ['agents', 'get_agent', 'list_assistants']:
            if hasattr(client, method_name):
                try:
                    method = getattr(client, method_name)
                    if callable(method):
                        result = method()
                        print(f"✅ {method_name} 成功: {type(result)}")
                except Exception as e:
                    print(f"❌ {method_name} 失败: {str(e)}")
        
    except ImportError as e:
        print(f"❌ 无法导入RAGFlow SDK: {str(e)}")
    except Exception as e:
        print(f"❌ RAGFlow API测试失败: {str(e)}")


if __name__ == "__main__":
    test_ragflow_api()
