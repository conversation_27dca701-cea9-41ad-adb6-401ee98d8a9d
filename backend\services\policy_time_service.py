"""
策略时间服务
包含策略时间有效性检查相关功能
"""

from typing import List, Dict, Any
from models.policy_object import PolicyObjectList


def check_policy_time_validity(policy_list: PolicyObjectList):
   """
   检查策略列表中每个策略的时间有效性，并打印每个对象的时间信息
   
   Args:
       policy_list: 策略对象列表
   """
   print(f"\n⏰ 开始执行策略时间有效性检查...")
   
   # 统计变量
   total_policies = len(policy_list)
   policies_with_time = 0
   valid_policies = 0
   expired_policies = 0
   expiring_soon_policies = 0
   invalid_time_policies = 0
   long_term_policies = 0
   
   # 分类存储策略
   expired_policy_details = []
   expiring_soon_details = []
   invalid_time_details = []
   
   try:
       for i, policy in enumerate(policy_list, 1):
           try:
               # 检查时间有效性
               time_result = policy.check_time_validity()
               
               # 统计有时间信息的策略
               if time_result['has_time_info']:
                   policies_with_time += 1
                   
                   # 根据时间状态进行分类统计
                   time_status = time_result['time_status']
                   
                   # 检查时间计算异常（结束时间有值但剩余天数为负或异常）
                   if time_result.get('end_date') and (time_result.get('days_until_expiry') is None or time_result.get('days_until_expiry') < 0):
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': policy.name_start_date,
                           'duration': policy.name_duration,
                           'end_date': time_result.get('end_date'),
                           'time_status': time_status,
                           'message': f"时间计算异常：剩余天数计算结果异常 ({time_result.get('days_until_expiry')})",
                           'warnings': time_result.get('warnings', []) + ['剩余天数计算异常']
                       })
                   elif time_status == 'expired':
                       expired_policies += 1
                       expired_policy_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': time_result['start_date'],
                           'duration': time_result['duration'],
                           'end_date': time_result['end_date'],
                           'days_until_expiry': time_result.get('days_until_expiry'),
                           'message': time_result['time_message'],
                           'warnings': time_result['warnings']
                       })
                       # 同时将过期策略也添加到时间格式错误列表中，便于统一查看问题
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': time_result['start_date'],
                           'duration': time_result['duration'],
                           'end_date': time_result['end_date'],
                           'time_status': time_status,
                           'message': f"策略已过期: {time_result['time_message']}",
                           'warnings': time_result['warnings'] + ['策略已过期，可能存在时间解析问题']
                       })
                   elif time_status == 'expiring_soon':
                       expiring_soon_policies += 1
                       expiring_soon_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': time_result['start_date'],
                           'duration': time_result['duration'],
                           'end_date': time_result['end_date'],
                           'days_until_expiry': time_result['days_until_expiry'],
                           'message': time_result['time_message']
                       })
                   elif time_status == 'active':
                       valid_policies += 1
                   elif time_status == 'invalid':
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': policy.name_start_date,
                           'duration': policy.name_duration,
                           'end_date': time_result.get('end_date'),
                           'time_status': time_status,
                           'message': time_result['time_message'],
                           'warnings': time_result['warnings']
                       })
                   elif time_status == 'unknown':
                       if time_result.get('warnings') or (policy.name_start_date and not time_result.get('end_date')):
                           invalid_time_policies += 1
                           invalid_time_details.append({
                               'index': i,
                               'id': policy.id,
                               'name': policy.name,
                               'start_date': policy.name_start_date,
                               'duration': policy.name_duration,
                               'end_date': time_result.get('end_date'),
                               'time_status': time_status,
                               'message': time_result['time_message'],
                               'warnings': time_result.get('warnings', [])
                           })
               else:
                   # 检查是否为长期策略
                   if policy.name_duration and policy.name_duration.lower() in ['长期', '永久', '无限期']:
                       long_term_policies += 1
                   elif policy.name_start_date:
                       invalid_time_policies += 1
                       invalid_time_details.append({
                           'index': i,
                           'id': policy.id,
                           'name': policy.name,
                           'start_date': policy.name_start_date,
                           'duration': policy.name_duration or "无",
                           'end_date': '无法计算',
                           'time_status': 'invalid',
                           'message': '有起始时间但持续时间无效或缺失',
                           'warnings': ['持续时间格式无效或缺失']
                       })
                       
           except Exception as e:
               print(f"❌ 处理策略 {i} (ID: {getattr(policy, 'id', '未知')}) 时出错: {str(e)}")
               invalid_time_policies += 1
               invalid_time_details.append({
                   'index': i,
                   'id': getattr(policy, 'id', '未知'),
                   'name': getattr(policy, 'name', '未知'),
                   'start_date': '处理异常',
                   'duration': '处理异常',
                   'end_date': '处理异常',
                   'time_status': 'error',
                   'message': f'处理异常: {str(e)}',
                   'warnings': [f'处理异常: {str(e)}']
               })
                   
       # 打印时间检查统计摘要
       print_time_validity_summary(
           total_policies, policies_with_time, valid_policies, 
           expired_policies, expiring_soon_policies, invalid_time_policies, 
           long_term_policies
       )
       
       # 打印时间格式错误策略的详细信息
       if invalid_time_details:
           from services.report_service import print_time_format_error_details
           print_time_format_error_details(invalid_time_details)
       
       # 以表格形式打印时间信息
       print_policies_time_table(policy_list)
           
   except Exception as e:
       print(f"❌ 时间有效性检查出错: {str(e)}")
       import traceback
       print(f"详细错误信息:")
       traceback.print_exc()


def print_time_validity_summary(total_policies: int, policies_with_time: int,
                                valid_policies: int, expired_policies: int,
                                expiring_soon_policies: int, invalid_time_policies: int,
                                long_term_policies: int):
    """
    打印时间有效性检查摘要
    """
    print(f"\n📊 时间有效性检查摘要")
    print("=" * 60)
    print(f"总策略数: {total_policies}")
    print(f"包含时间信息的策略: {policies_with_time} ({policies_with_time / total_policies * 100:.1f}%)")
    print(f"长期策略: {long_term_policies}")
    print(f"无时间信息策略: {total_policies - policies_with_time - long_term_policies}")

    if policies_with_time > 0:
        print(f"\n⏰ 时间状态分布:")
        print(f"  ✅ 有效策略: {valid_policies} ({valid_policies / policies_with_time * 100:.1f}%)")
        print(
            f"  ⚠️  即将过期策略: {expiring_soon_policies} ({expiring_soon_policies / policies_with_time * 100:.1f}%)")
        print(f"  ❌ 已过期策略: {expired_policies} ({expired_policies / policies_with_time * 100:.1f}%)")
        print(
            f"  🔧 时间格式错误策略: {invalid_time_policies} ({invalid_time_policies / policies_with_time * 100:.1f}%)")

        # 风险提示
        if expired_policies > 0:
            print(f"\n🚨 发现 {expired_policies} 个已过期策略，建议立即处理！")
        if expiring_soon_policies > 0:
            print(f"⚠️  发现 {expiring_soon_policies} 个即将过期策略，建议提前更新！")
        if invalid_time_policies > 0:
            print(f"🔧 发现 {invalid_time_policies} 个时间格式错误策略，建议检查策略名称格式！")


def print_all_policies_time_info(policy_list: PolicyObjectList):
    """
    专门用于打印所有策略的时间信息的函数

    Args:
        policy_list: 策略对象列表
    """
    print(f"\n📋 所有策略时间信息表:")
    print("=" * 100)
    print(
        f"{'序号':<4} {'策略ID':<8} {'起始时间':<12} {'持续时间':<15} {'计算结束时间':<12} {'状态':<10} {'策略名称':<40}")
    print("-" * 100)

    for i, policy in enumerate(policy_list, 1):
        time_result = policy.check_time_validity()

        # 准备显示数据
        policy_id = policy.id[:7] if len(policy.id) > 7 else policy.id
        start_date = policy.name_start_date if policy.name_start_date else "无"
        duration = policy.name_duration if policy.name_duration else "无"
        end_date = time_result['end_date'] if time_result['end_date'] else "无"
        status = time_result['time_status']
        policy_name = policy.name[:38] + "..." if len(policy.name) > 38 else policy.name

        # 根据状态添加颜色标识
        status_icon = {
            'active': '✅',
            'expired': '❌',
            'expiring_soon': '⚠️',
            'invalid': '🔧',
            'unknown': '❓'
        }.get(status, '⭕')

        print(
            f"{i:<4} {policy_id:<8} {start_date:<12} {duration:<15} {end_date:<12} {status_icon}{status:<9} {policy_name:<40}")


def print_policies_time_table(policy_list: PolicyObjectList):
    """
    以表格形式打印所有策略的时间信息

    Args:
        policy_list: 策略对象列表
    """
    print(f"\n📊 策略时间信息表 (共 {len(policy_list)} 条)")
    print("=" * 120)

    # 表头
    headers = ['序号', '策略ID', '起始时间', '持续时间', '结束时间', '剩余天数', '状态', '策略名称(前30字符)']
    col_widths = [4, 8, 12, 15, 12, 8, 12, 35]

    # 打印表头
    header_line = ""
    for i, (header, width) in enumerate(zip(headers, col_widths)):
        header_line += f"{header:<{width}} "
    print(header_line)
    print("-" * 120)

    # 记录时间格式错误的策略
    time_format_errors = []

    # 打印数据行
    for i, policy in enumerate(policy_list, 1):
        try:
            time_result = policy.check_time_validity()

            # 检查是否为时间格式错误（包括过期策略）
            if time_result.get('time_status') in ['invalid', 'expired'] or time_result.get('warnings'):
                time_format_errors.append({
                    'index': i,
                    'id': policy.id,
                    'name': policy.name,
                    'start_date': policy.name_start_date,
                    'duration': policy.name_duration,
                    'time_status': time_result.get('time_status', 'unknown'),
                    'message': time_result.get('time_message', ''),
                    'warnings': time_result.get('warnings', [])
                })

            # 准备数据，处理None值
            row_data = [
                str(i),
                (policy.id[:7] if policy.id and len(policy.id) > 7 else policy.id) or "无",
                policy.name_start_date or "无",
                policy.name_duration or "无",
                time_result.get('end_date') or '无',
                str(time_result.get('days_until_expiry')) if time_result.get('days_until_expiry') is not None else '无',
                time_result.get('time_status', 'unknown'),
                (policy.name[:32] + "..." if policy.name and len(policy.name) > 32 else policy.name) or "无名称"
            ]

            # 打印行
            row_line = ""
            for j, (data, width) in enumerate(zip(row_data, col_widths)):
                row_line += f"{str(data):<{width}} "
            print(row_line)

        except Exception as e:
            # 如果单个策略处理出错，记录错误但继续处理其他策略
            print(
                f"{i:<4} {'ERROR':<8} {'处理出错':<12} {'处理出错':<15} {'处理出错':<12} {'处理出错':<8} {'error':<12} {str(e)[:35]:<35}")
            time_format_errors.append({
                'index': i,
                'id': policy.id if hasattr(policy, 'id') else '未知',
                'name': policy.name if hasattr(policy, 'name') else '未知',
                'start_date': '处理异常',
                'duration': '处理异常',
                'time_status': 'error',
                'message': f'处理异常: {str(e)}',
                'warnings': [f'处理异常: {str(e)}']
            })

    print("=" * 120)

    # 打印时间格式错误详情
    if time_format_errors:
        from services.report_service import print_time_format_error_details
        print_time_format_error_details(time_format_errors)
