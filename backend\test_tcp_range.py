"""
测试TCP端口范围检查功能
"""

from models.policy_object import PolicyObject
import time


def test_tcp_range_detection():
    """测试TCP端口范围检测功能"""
    
    print("🧪 测试TCP端口范围检测功能")
    print("=" * 60)
    
    # 测试不同的TCP端口范围
    test_cases = [
        {
            "name": "小范围TCP端口 (1-100)",
            "services": ["TCP 1-100"],
            "expected": "应该发现一些风险端口"
        },
        {
            "name": "中等范围TCP端口 (20-25)",
            "services": ["TCP 20-25"],
            "expected": "包含FTP相关端口"
        },
        {
            "name": "大范围TCP端口 (1-5000)",
            "services": ["TCP 1-5000"],
            "expected": "应该发现很多风险端口"
        },
        {
            "name": "超大范围TCP端口 (1-20000)",
            "services": ["TCP 1-20000"],
            "expected": "超出检查限制"
        },
        {
            "name": "高端口范围 (8000-8100)",
            "services": ["TCP 8000-8100"],
            "expected": "可能较安全"
        },
        {
            "name": "混合服务",
            "services": ["TCP 21", "TCP 1-50", "UDP 53"],
            "expected": "混合风险检测"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n【测试案例 {i}】{test_case['name']}")
        print(f"期望结果: {test_case['expected']}")
        print("-" * 40)
        
        # 记录开始时间
        start_time = time.time()
        
        # 创建策略对象
        policy_data = {
            "id": f"TCP_TEST_{i:03d}",
            "name": f"TCP范围测试_{i}",
            "src_zone": "internal",
            "src_addr": ["192.168.1.0/24"],
            "dst_zone": "external",
            "dst_addr": ["any"],
            "service": test_case['services'],
            "action": "允许",
            "hit_count": "1000"
        }
        
        policy = PolicyObject.from_dict(policy_data)
        
        # 检测服务风险
        risk_result = policy.check_service_risks()
        
        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"服务列表: {test_case['services']}")
        print(f"处理时间: {processing_time:.3f} 秒")
        print(f"检测结果: {risk_result['summary']}")
        
        # 详细显示风险服务
        if risk_result.get('risk_services'):
            print(f"风险服务详情:")
            for risk_service in risk_result['risk_services']:
                risk_icon = "🔴" if risk_service['risk_level'] == 'high' else "🟡"
                print(f"  {risk_icon} {risk_service['service']}")
                print(f"    风险级别: {risk_service['risk_level']}")
                print(f"    分类: {risk_service.get('category', 'N/A')}")
                
                # 显示详细信息（截断长文本）
                if risk_service.get('details'):
                    detail = risk_service['details'][0]
                    if len(detail) > 100:
                        detail = detail[:100] + "..."
                    print(f"    详情: {detail}")
        
        # 显示安全和未知服务统计
        if risk_result.get('safe_services'):
            print(f"✅ 安全服务: {len(risk_result['safe_services'])}个")
        
        if risk_result.get('unknown_services'):
            print(f"❓ 未知服务: {len(risk_result['unknown_services'])}个")
        
        # 性能评估
        if processing_time > 1.0:
            print(f"⚠️  处理时间较长: {processing_time:.3f}秒")
        elif processing_time > 0.1:
            print(f"⏱️  处理时间适中: {processing_time:.3f}秒")
        else:
            print(f"⚡ 处理速度快: {processing_time:.3f}秒")


def test_performance_comparison():
    """测试性能对比"""
    
    print(f"\n🚀 性能测试对比")
    print("=" * 60)
    
    # 测试不同大小的端口范围
    range_sizes = [
        ("小范围", "TCP 1-50", 50),
        ("中范围", "TCP 1-500", 500),
        ("大范围", "TCP 1-2000", 2000),
        ("超大范围", "TCP 1-10000", 10000),
        ("极大范围", "TCP 1-20000", 20000)
    ]
    
    for name, service, size in range_sizes:
        print(f"\n{name} ({size}个端口): {service}")
        
        start_time = time.time()
        
        policy_data = {
            "id": "PERF_TEST",
            "name": "性能测试策略",
            "src_zone": "any",
            "src_addr": ["any"],
            "dst_zone": "any",
            "dst_addr": ["any"],
            "service": [service],
            "action": "允许",
            "hit_count": "0"
        }
        
        policy = PolicyObject.from_dict(policy_data)
        risk_result = policy.check_service_risks()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"  处理时间: {processing_time:.3f}秒")
        print(f"  检测结果: {risk_result['summary']}")
        
        # 计算每秒处理的端口数
        if processing_time > 0:
            ports_per_second = size / processing_time
            print(f"  处理速度: {ports_per_second:.0f} 端口/秒")


def test_specific_tcp_ports():
    """测试特定的TCP风险端口"""
    
    print(f"\n🎯 特定TCP风险端口测试")
    print("=" * 60)
    
    # 测试一些已知的风险端口范围
    specific_tests = [
        ("FTP相关", "TCP 20-22"),
        ("Telnet相关", "TCP 23-24"), 
        ("SMTP相关", "TCP 25-26"),
        ("低端口范围", "TCP 0-10"),
        ("Web相关", "TCP 80-81"),
        ("高危端口", "TCP 135-139"),
        ("NetBIOS", "TCP 445-446")
    ]
    
    for name, service in specific_tests:
        print(f"\n{name}: {service}")
        
        policy_data = {
            "id": "SPECIFIC_TEST",
            "name": f"特定端口测试-{name}",
            "src_zone": "any",
            "src_addr": ["any"],
            "dst_zone": "any", 
            "dst_addr": ["any"],
            "service": [service],
            "action": "允许",
            "hit_count": "0"
        }
        
        policy = PolicyObject.from_dict(policy_data)
        risk_result = policy.check_service_risks()
        
        print(f"  检测结果: {risk_result['summary']}")
        
        if risk_result.get('risk_services'):
            for risk_service in risk_result['risk_services']:
                if risk_service.get('details'):
                    detail = risk_service['details'][0]
                    # 提取端口列表
                    if "发现TCP风险端口:" in detail:
                        ports_info = detail.split("发现TCP风险端口:")[1].strip()
                        print(f"  风险端口: {ports_info}")


if __name__ == "__main__":
    test_tcp_range_detection()
    test_performance_comparison()
    test_specific_tcp_ports()
