"""
数据类型检查工具类
用于检查和处理前端传来的不同类型数据
"""

import json
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum


class DataType(Enum):
    """数据类型枚举"""
    POLICY_DATA = "policy_data"
    FORM_DATA = "form_data"
    UNKNOWN = "unknown"


class DataTypeChecker:
    """数据类型检查器"""
    
    @staticmethod
    def check_data_type(request_data: Dict[str, Any]) -> Tuple[DataType, Dict[str, Any]]:
        """
        检查请求数据类型并返回详细信息
        
        Args:
            request_data: 前端请求数据
            
        Returns:
            Tuple[DataType, Dict]: (数据类型, 详细信息)
        """
        if not isinstance(request_data, dict):
            return DataType.UNKNOWN, {
                "error": "请求数据不是字典格式",
                "data_format": type(request_data).__name__
            }
        
        # 检查是否有type字段
        if "type" not in request_data:
            return DataType.UNKNOWN, {
                "error": "请求中缺少数据类型字段",
                "available_keys": list(request_data.keys())
            }
        
        data_type_str = request_data.get("type", "").lower()
        
        if data_type_str == "policy_data":
            return DataType.POLICY_DATA, DataTypeChecker._analyze_policy_data(request_data)
        elif data_type_str == "form_data":
            return DataType.FORM_DATA, DataTypeChecker._analyze_form_data(request_data)
        else:
            return DataType.UNKNOWN, {
                "error": f"未知数据类型: {data_type_str}",
                "received_type": data_type_str
            }
    
    @staticmethod
    def _analyze_policy_data(request_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析策略数据"""
        analysis = {
            "type": "policy_data",
            "valid": True,
            "errors": []
        }
        
        # 检查count字段
        count = request_data.get("count", 0)
        analysis["declared_count"] = count
        
        # 检查data字段
        data = request_data.get("data", [])
        if not isinstance(data, list):
            analysis["valid"] = False
            analysis["errors"].append("data字段不是数组格式")
            analysis["actual_count"] = 0
        else:
            analysis["actual_count"] = len(data)
            
            # 检查count是否匹配
            if count != len(data):
                analysis["errors"].append(f"声明数量({count})与实际数量({len(data)})不匹配")
            
            # 分析策略记录结构
            if data:
                first_record = data[0]
                analysis["record_structure"] = DataTypeChecker._analyze_policy_record_structure(first_record)
                
                # 检查所有记录是否有name字段
                records_with_name = sum(1 for record in data if record.get("name", "").strip())
                analysis["records_with_name"] = records_with_name
                analysis["records_without_name"] = len(data) - records_with_name
        
        return analysis
    
    @staticmethod
    def _analyze_form_data(request_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析表单数据"""
        analysis = {
            "type": "form_data",
            "valid": True,
            "errors": []
        }
        
        # 检查items字段
        items = request_data.get("items", [])
        if not isinstance(items, list):
            analysis["valid"] = False
            analysis["errors"].append("items字段不是数组格式")
            analysis["item_count"] = 0
        else:
            analysis["item_count"] = len(items)
            
            # 分析表单项结构
            if items:
                valid_items = 0
                for item in items:
                    if isinstance(item, dict) and "key" in item and "value" in item:
                        valid_items += 1
                
                analysis["valid_items"] = valid_items
                analysis["invalid_items"] = len(items) - valid_items
        
        return analysis
    
    @staticmethod
    def _analyze_policy_record_structure(record: Dict[str, Any]) -> Dict[str, Any]:
        """分析策略记录结构"""
        expected_fields = ["id", "name", "src_zone", "src_addr", "dst_zone", "dst_addr", "service", "action", "hit_count"]
        
        structure = {
            "total_fields": len(record),
            "expected_fields": len(expected_fields),
            "missing_fields": [],
            "extra_fields": [],
            "field_types": {}
        }
        
        # 检查缺失字段
        for field in expected_fields:
            if field not in record:
                structure["missing_fields"].append(field)
            else:
                structure["field_types"][field] = type(record[field]).__name__
        
        # 检查额外字段
        for field in record:
            if field not in expected_fields:
                structure["extra_fields"].append(field)
        
        return structure
    
    @staticmethod
    def print_analysis_report(data_type: DataType, analysis: Dict[str, Any]) -> None:
        """打印数据分析报告"""
        print(f"\n🔍 数据类型检查报告")
        print("=" * 60)
        
        if data_type == DataType.POLICY_DATA:
            DataTypeChecker._print_policy_analysis(analysis)
        elif data_type == DataType.FORM_DATA:
            DataTypeChecker._print_form_analysis(analysis)
        else:
            DataTypeChecker._print_unknown_analysis(analysis)
        
        print("=" * 60)
    
    @staticmethod
    def _print_policy_analysis(analysis: Dict[str, Any]) -> None:
        """打印策略数据分析"""
        print(f"📊 数据类型: 策略数据")
        print(f"✅ 数据有效性: {'有效' if analysis['valid'] else '无效'}")
        
        if analysis.get("errors"):
            print(f"❌ 错误信息:")
            for error in analysis["errors"]:
                print(f"   - {error}")
        
        print(f"📈 数据统计:")
        print(f"   声明数量: {analysis.get('declared_count', 'N/A')}")
        print(f"   实际数量: {analysis.get('actual_count', 'N/A')}")
        print(f"   有名称记录: {analysis.get('records_with_name', 'N/A')}")
        print(f"   无名称记录: {analysis.get('records_without_name', 'N/A')}")
        
        if "record_structure" in analysis:
            structure = analysis["record_structure"]
            print(f"🏗️  记录结构:")
            print(f"   总字段数: {structure['total_fields']}")
            print(f"   期望字段数: {structure['expected_fields']}")
            
            if structure["missing_fields"]:
                print(f"   缺失字段: {', '.join(structure['missing_fields'])}")
            
            if structure["extra_fields"]:
                print(f"   额外字段: {', '.join(structure['extra_fields'])}")
    
    @staticmethod
    def _print_form_analysis(analysis: Dict[str, Any]) -> None:
        """打印表单数据分析"""
        print(f"📝 数据类型: 表单数据")
        print(f"✅ 数据有效性: {'有效' if analysis['valid'] else '无效'}")
        
        if analysis.get("errors"):
            print(f"❌ 错误信息:")
            for error in analysis["errors"]:
                print(f"   - {error}")
        
        print(f"📈 数据统计:")
        print(f"   表单项数量: {analysis.get('item_count', 'N/A')}")
        print(f"   有效项数量: {analysis.get('valid_items', 'N/A')}")
        print(f"   无效项数量: {analysis.get('invalid_items', 'N/A')}")
    
    @staticmethod
    def _print_unknown_analysis(analysis: Dict[str, Any]) -> None:
        """打印未知数据分析"""
        print(f"❓ 数据类型: 未知")
        print(f"❌ 错误信息: {analysis.get('error', 'N/A')}")
        
        if "available_keys" in analysis:
            print(f"📋 可用字段: {', '.join(analysis['available_keys'])}")
        
        if "received_type" in analysis:
            print(f"🔤 接收到的类型: {analysis['received_type']}")


# 使用示例
def example_usage():
    """使用示例"""
    # 策略数据示例
    policy_request = {
        "type": "policy_data",
        "count": 2,
        "data": [
            {
                "id": "1",
                "name": "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
                "src_zone": "any",
                "src_addr": ["192.168.1.1"],
                "dst_zone": "any",
                "dst_addr": ["any"],
                "service": ["HTTP"],
                "action": "允许",
                "hit_count": "1000"
            }
        ]
    }
    
    data_type, analysis = DataTypeChecker.check_data_type(policy_request)
    DataTypeChecker.print_analysis_report(data_type, analysis)


if __name__ == "__main__":
    example_usage()
