<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .success {
            background-color: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        .error {
            background-color: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }
        .info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1565c0;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .test-button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API配置测试</h1>
        
        <div class="test-section info">
            <h3>配置修改说明</h3>
            <p>已在 <code>config.ts</code> 中添加新的风险分析接口端点：</p>
            <div class="code">
// API端点
export const API_ENDPOINTS = {
  // 健康检查API
  HEALTH: '/health',
  // 图片解析API
  IMAGE_PARSE: '/image/parse',
  // 表单数据提交API
  FORM_DATA_SUBMIT: '/form/submit',
  // 表单验证API
  FORM_VALIDATE: '/form/validate',
  // 风险分析API ← 新增
  RISK_ANALYSIS: '/form/risk-analysis'
};
            </div>
        </div>

        <div class="test-section info">
            <h3>DomCapture.vue 修改说明</h3>
            <p>修改了风险分析接口调用方式：</p>
            <div class="code">
// 修改前（错误）
const riskApiUrl = await getApiUrl('risk-analysis');

// 修改后（正确）
const riskApiUrl = await getApiUrl(API_ENDPOINTS.RISK_ANALYSIS);
            </div>
        </div>

        <div class="test-section">
            <h3>API端点测试</h3>
            <p>点击按钮测试各个API端点的URL生成：</p>
            
            <button class="test-button" onclick="testEndpoint('HEALTH')">测试健康检查</button>
            <button class="test-button" onclick="testEndpoint('FORM_VALIDATE')">测试表单验证</button>
            <button class="test-button" onclick="testEndpoint('RISK_ANALYSIS')">测试风险分析</button>
            
            <div id="test-results" style="margin-top: 15px;"></div>
        </div>

        <div class="test-section">
            <h3>完整的接口调用流程</h3>
            <ol>
                <li><strong>用户点击"提交数据"</strong></li>
                <li><strong>调用原有接口：</strong>
                    <div class="code">POST http://127.0.0.1:8000/form/validate</div>
                </li>
                <li><strong>如果是策略数据，调用新接口：</strong>
                    <div class="code">POST http://127.0.0.1:8000/form/risk-analysis</div>
                </li>
                <li><strong>分别处理两个接口的响应</strong></li>
                <li><strong>在UI上显示两部分结果：</strong>
                    <ul>
                        <li>修改建议（蓝色区域）</li>
                        <li>风险分析报告（橙色区域）</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="test-section success">
            <h3>✅ 修复完成</h3>
            <p>现在插件应该能够正确调用两个接口：</p>
            <ul>
                <li>✅ 使用正确的API_ENDPOINTS配置</li>
                <li>✅ 通过getApiUrl()函数生成完整URL</li>
                <li>✅ 支持用户自定义的host和port配置</li>
                <li>✅ 保持与现有配置系统的一致性</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>后续测试建议</h3>
            <ol>
                <li>确保后端服务器运行在配置的端口（默认8000）</li>
                <li>在浏览器插件中测试策略数据提交</li>
                <li>检查浏览器开发者工具的Network面板，确认两个接口都被调用</li>
                <li>验证风险分析结果是否正确显示在橙色区域</li>
            </ol>
        </div>
    </div>

    <script>
        // 模拟API_ENDPOINTS配置
        const API_ENDPOINTS = {
            HEALTH: '/health',
            IMAGE_PARSE: '/image/parse',
            FORM_DATA_SUBMIT: '/form/submit',
            FORM_VALIDATE: '/form/validate',
            RISK_ANALYSIS: '/form/risk-analysis'
        };

        // 模拟getApiUrl函数
        async function getApiUrl(endpoint) {
            const baseUrl = 'http://127.0.0.1:8000';
            return `${baseUrl}${endpoint}`;
        }

        // 测试端点URL生成
        async function testEndpoint(endpointName) {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                const endpoint = API_ENDPOINTS[endpointName];
                const fullUrl = await getApiUrl(endpoint);
                
                resultsDiv.innerHTML += `
                    <div class="test-section success">
                        <strong>${endpointName}:</strong><br>
                        端点: <code>${endpoint}</code><br>
                        完整URL: <code>${fullUrl}</code>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-section error">
                        <strong>${endpointName} 测试失败:</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 页面加载时显示当前配置
        window.onload = function() {
            console.log('API_ENDPOINTS配置:', API_ENDPOINTS);
        };
    </script>
</body>
</html>
