"""
测试当前的API接口
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routers.form_router import handle_form_validation

async def test_current_api():
    """测试当前的API接口"""
    
    print("🧪 测试当前的API接口")
    print("=" * 50)
    
    # 测试1：表单数据
    print("\n1️⃣ 测试表单数据...")
    
    form_request = {
        "type": "form_data",
        "items": [
            {"key": "策略名称", "value": "220519-办公网-DMZ区-张三-1年"},
            {"key": "源地址", "value": "192.168.1.0/24"},
            {"key": "目标地址", "value": "10.0.0.1"},
            {"key": "服务", "value": "TCP 80"},
            {"key": "动作", "value": "允许"}
        ]
    }
    
    result = await handle_form_validation(form_request)
    print(f"响应: {result}")
    print(f"响应码: {result.get('code')}")
    print(f"消息: {result.get('msg')}")
    print(f"数据: {result.get('data')}")
    
    # 测试2：策略数据
    print("\n2️⃣ 测试策略数据...")
    
    policy_request = {
        "type": "policy_data",
        "data": [
            {
                "id": "1",
                "name": "220519-办公网-DMZ区-张三-1年",
                "src_zone": "internal",
                "src_addr": ["192.168.1.0/24"],
                "dst_zone": "external", 
                "dst_addr": ["any"],
                "service": ["TCP 80"],
                "action": "允许",
                "hit_count": "100"
            }
        ],
        "count": 1
    }
    
    result2 = await handle_form_validation(policy_request)
    print(f"响应: {result2}")
    print(f"响应码: {result2.get('code')}")
    print(f"消息: {result2.get('msg')}")
    print(f"数据: {result2.get('data')}")

if __name__ == "__main__":
    asyncio.run(test_current_api())
