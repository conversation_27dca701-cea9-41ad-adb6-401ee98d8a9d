"""
测试大模型集成功能
验证策略申请分析的完整流程
"""

import requests
import json


def test_llm_integration():
    """测试大模型集成功能"""
    
    print("🧪 测试大模型集成功能")
    print("=" * 80)
    
    # 测试用例
    test_cases = [
        {
            "name": "完整的策略申请信息",
            "data": {
                "items": [
                    {"key": "申请人", "value": "张三"},
                    {"key": "部门", "value": "IT部"},
                    {"key": "申请时间", "value": "2024-08-03"},
                    {"key": "申请原因", "value": "业务系统需要访问数据库服务器"},
                    {"key": "源地址", "value": "192.168.1.100"},
                    {"key": "目的地址", "value": "10.0.0.50"},
                    {"key": "目的端口", "value": "3306"},  # MySQL端口
                    {"key": "协议", "value": "TCP"},
                    {"key": "业务描述", "value": "ERP系统连接MySQL数据库"},
                    {"key": "预期使用时间", "value": "长期使用"}
                ]
            }
        },
        {
            "name": "高风险端口申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "李四"},
                    {"key": "部门", "value": "开发部"},
                    {"key": "申请原因", "value": "测试需要"},
                    {"key": "目的端口", "value": "1234"},  # 高风险端口
                    {"key": "协议", "value": "TCP"}
                ]
            }
        },
        {
            "name": "大范围端口申请",
            "data": {
                "items": [
                    {"key": "申请人", "value": "王五"},
                    {"key": "部门", "value": "运维部"},
                    {"key": "申请原因", "value": "系统监控需要"},
                    {"key": "目的端口", "value": "8000-9000"},  # 大范围端口
                    {"key": "协议", "value": "TCP"}
                ]
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 60)
        
        test_data = test_case['data']
        
        # 显示测试数据
        print(f"📊 测试数据:")
        for item in test_data['items']:
            print(f"  - {item['key']}: {item['value']}")
        
        try:
            # 发送请求
            url = "http://127.0.0.1:8000/policy-application/detect"
            headers = {"Content-Type": "application/json"}
            
            print(f"\n🔄 发送请求到: {url}")
            response = requests.post(url, json=test_data, headers=headers, timeout=120)  # 增加超时时间
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                detection_data = result.get('data', {})
                
                print(f"✅ 请求成功!")
                print(f"📋 检测结果:")
                print(f"  - 处理状态: {detection_data.get('status', 'N/A')}")
                print(f"  - 处理数量: {detection_data.get('items_processed', 0)}")
                print(f"  - 是否有风险: {detection_data.get('has_risks', False)}")
                
                # 显示提取的字段
                extracted = detection_data.get('extracted_fields', {})
                print(f"  - 提取的端口: {extracted.get('destination_port', '未找到')}")
                print(f"  - 提取的协议: {extracted.get('protocol', '未找到')}")
                
                # 显示端口风险
                risks = detection_data.get('port_risks', [])
                if risks:
                    print(f"  - 发现 {len(risks)} 个风险:")
                    for j, risk in enumerate(risks, 1):
                        print(f"    {j}. {risk['protocol']}:{risk['port']} - {risk['risk_level']}")
                        print(f"       {risk['reason']}")
                else:
                    print(f"  - 未发现端口风险")
                
                # 显示大模型分析结果
                llm_analysis = detection_data.get('llm_analysis', '')
                if llm_analysis:
                    print(f"\n🤖 大模型分析结果:")
                    print("-" * 40)
                    print(llm_analysis)
                    print("-" * 40)
                else:
                    print(f"\n❌ 未获得大模型分析结果")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败: 请确保后端服务已启动")
            break
        except requests.exceptions.Timeout:
            print("❌ 请求超时: 大模型分析可能需要较长时间")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")


def test_ragflow_service_directly():
    """直接测试RAGFlow服务"""
    
    print(f"\n" + "="*80)
    print("🔧 直接测试RAGFlow服务")
    print("="*80)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from services.ragflow_service import analyze_policy_application_data
        
        # 测试数据
        test_details = """申请人: 张三
部门: IT部
申请原因: 业务系统需要访问数据库服务器
目的端口: 3306
协议: TCP
业务描述: ERP系统连接MySQL数据库"""
        
        print(f"📊 测试数据:")
        print(test_details)
        
        print(f"\n🔄 开始调用大模型分析...")
        
        result = analyze_policy_application_data(test_details)
        
        print(f"✅ 大模型分析完成!")
        print(f"📋 分析结果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
    except ImportError as e:
        print(f"❌ 无法导入RAGFlow服务: {str(e)}")
    except Exception as e:
        print(f"❌ 大模型分析失败: {str(e)}")


def main():
    """主测试函数"""
    
    print("🚀 开始大模型集成测试")
    
    # 直接测试RAGFlow服务
    test_ragflow_service_directly()
    
    # 测试完整的API集成
    test_llm_integration()
    
    print(f"\n" + "="*80)
    print("📊 测试总结")
    print("="*80)
    print("✅ 大模型集成测试完成")
    print("\n📋 功能说明:")
    print("  1. 策略申请检测API接收键值对数据")
    print("  2. 进行端口风险检测")
    print("  3. 调用RAGFlow大模型进行智能分析")
    print("  4. 返回包含端口风险和大模型分析的完整结果")
    print("  5. 前端显示端口风险检测和智能分析建议")
    print("\n🚀 启动后端服务命令:")
    print("  cd backend")
    print("  python -m uvicorn main:app --reload")
    print("\n🌐 API端点:")
    print("  - POST /policy-application/detect - 策略申请检测（含大模型分析）")
    print("\n⚠️ 注意事项:")
    print("  - 大模型分析可能需要1-2分钟时间")
    print("  - 确保RAGFlow服务可访问")
    print("  - 智能体ID需要正确配置")


if __name__ == "__main__":
    main()
