# RAGFlow智能体服务

## 📋 概述

简化的RAGFlow智能体服务，专门用于与RAGFlow智能体进行对话。

## 🔧 核心功能

### RAGFlowService 类

提供一个静态方法用于与智能体对话：

```python
@staticmethod
def ask_agent(prompt: str, agent_id: Optional[str] = None, stream: bool = True) -> str
```

### Config 配置类

包含所有必要的配置参数：

```python
class Config:
    # UMI-OCR 配置
    UMI_OCR_API_URL = "http://localhost:1224/api/ocr"
    
    # RAGFlow 配置
    RAGFLOW_API_KEY = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm"
    RAGFLOW_BASE_URL = "http://*************:8011"
    RAGFLOW_ASSISTANT_ID = "00c390745ed211f0bb210242ac150006"
    
    # 文件配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}
```

## 🚀 使用方法

### 基础使用

```python
from services.ragflow_service import RAGFlowService

# 向智能体提问
response = RAGFlowService.ask_agent("请介绍一下防火墙策略的最佳实践")
print(response)
```

### 指定智能体ID

```python
# 使用特定的智能体
response = RAGFlowService.ask_agent(
    "分析这个网络配置", 
    agent_id="your-custom-agent-id"
)
```

### 选择响应模式

```python
# 流式响应（默认）
response = RAGFlowService.ask_agent("你的问题", stream=True)

# 非流式响应
response = RAGFlowService.ask_agent("你的问题", stream=False)
```

### 访问配置

```python
from services.ragflow_service import config

print(f"RAGFlow URL: {config.RAGFLOW_BASE_URL}")
print(f"智能体ID: {config.RAGFLOW_ASSISTANT_ID}")
```

## 📁 文件结构

```
backend/services/
├── ragflow_service.py              # 核心服务文件
├── test_simple_ragflow.py          # 测试文件
├── ragflow_usage_example.py        # 使用示例
└── RAGFLOW_SERVICE_README.md       # 本说明文档
```

## 🔍 特性

- **简洁设计**: 只保留核心的智能体对话功能
- **容错处理**: 完善的错误处理和异常捕获
- **灵活配置**: 支持自定义智能体ID和响应模式
- **向后兼容**: 保持与现有代码的兼容性
- **自动降级**: RAGFlow SDK不可用时自动处理

## ⚠️ 注意事项

1. **依赖**: 需要安装 `ragflow_sdk`，如果未安装会自动处理
2. **网络**: 确保能够访问RAGFlow服务地址
3. **认证**: 确保API密钥有效且有足够权限
4. **智能体**: 确保智能体ID存在且可访问

## 🧪 测试

运行测试验证功能：

```bash
cd backend
python test_simple_ragflow.py
```

查看使用示例：

```bash
python ragflow_usage_example.py
```

## 📞 错误处理

所有方法都包含完善的错误处理：

- `RAGFlow SDK 不可用`: SDK未安装时的提示
- `RAGFlow 客户端未初始化`: 客户端初始化失败时的提示
- `未找到智能体 ID`: 智能体不存在时的提示
- 其他网络或服务错误的详细信息

---

**简化完成时间**: 2025-08-02  
**版本**: v1.0 (简化版)  
**功能**: 专注于智能体对话
