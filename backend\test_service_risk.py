"""
测试服务风险检测功能
"""

from models.policy_object import PolicyObject


def test_service_risk_detection():
    """测试服务风险检测功能"""
    
    print("🧪 测试服务风险检测功能")
    print("=" * 60)
    
    # 测试数据
    test_cases = [
        {
            "name": "测试案例1 - 混合服务",
            "services": ["TCP 21", "UDP 53", "ICMP", "any", "TCP 5558"]
        },
        {
            "name": "测试案例2 - 端口范围",
            "services": ["UDP 50000-60000", "TCP 1-100", "UDP_47000-48000"]
        },
        {
            "name": "测试案例3 - 特殊格式",
            "services": ["TCP_5432", "UDP_54321", "TCP 443", "UDP 31337"]
        },
        {
            "name": "测试案例4 - 高风险端口",
            "services": ["TCP 0", "TCP 1", "UDP 30003", "TCP 31337"]
        },
        {
            "name": "测试案例5 - 安全端口",
            "services": ["TCP 8080", "UDP 12345", "ICMP"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n【测试案例 {i}】{test_case['name']}")
        print("-" * 40)
        
        # 创建策略对象
        policy_data = {
            "id": str(i),
            "name": f"测试策略_{i}",
            "src_zone": "any",
            "src_addr": ["192.168.1.0/24"],
            "dst_zone": "any", 
            "dst_addr": ["any"],
            "service": test_case['services'],
            "action": "允许",
            "hit_count": "100"
        }
        
        policy = PolicyObject.from_dict(policy_data)
        
        # 检测服务风险
        risk_result = policy.check_service_risks()
        
        print(f"服务列表: {test_case['services']}")
        print(f"检测结果: {risk_result['summary']}")
        
        if risk_result.get('risk_services'):
            print(f"风险服务详情:")
            for risk_service in risk_result['risk_services']:
                risk_icon = "🔴" if risk_service['risk_level'] == 'high' else "🟡"
                print(f"  {risk_icon} {risk_service['service']}")
                print(f"    分类: {risk_service.get('category', 'N/A')}")
                if risk_service.get('details'):
                    detail = risk_service['details'][0]
                    if len(detail) > 80:
                        detail = detail[:80] + "..."
                    print(f"    详情: {detail}")
        
        if risk_result.get('safe_services'):
            print(f"安全服务: {len(risk_result['safe_services'])}个")
        
        if risk_result.get('unknown_services'):
            print(f"未知服务: {len(risk_result['unknown_services'])}个")
            for unknown in risk_result['unknown_services']:
                print(f"  ❓ {unknown['service']}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")


def test_service_parsing():
    """测试服务字符串解析功能"""
    
    print("\n🔍 测试服务字符串解析功能")
    print("=" * 60)
    
    # 创建一个测试策略对象
    policy = PolicyObject()
    
    test_services = [
        "TCP 21",
        "UDP 53", 
        "TCP_5432",
        "UDP_50000-60000",
        "TCP 1-100",
        "ICMP",
        "any",
        "5558",
        "invalid_format",
        "UDP 54321",
        "TCP 31337"
    ]
    
    for service in test_services:
        parsed = policy._parse_service_string(service)
        print(f"原始: {service:20} -> 解析: {parsed}")


def test_detailed_risk_report():
    """测试详细风险报告"""
    
    print("\n📋 测试详细风险报告")
    print("=" * 60)
    
    # 创建包含各种服务的策略
    policy_data = {
        "id": "999",
        "name": "综合测试策略",
        "src_zone": "internal",
        "src_addr": ["10.0.0.0/8"],
        "dst_zone": "external",
        "dst_addr": ["any"],
        "service": [
            "TCP 21",      # FTP - 受控使用
            "TCP 23",      # Telnet - 受控使用  
            "TCP 0",       # 无效端口 - 禁止使用
            "UDP 54321",   # 高风险UDP端口
            "TCP 443",     # HTTPS - 受控使用
            "UDP 50000-60000",  # 大范围UDP
            "ICMP",        # 安全协议
            "any",         # 任意服务
            "TCP_8080",    # 可能安全的端口
            "invalid_service"  # 无效格式
        ],
        "action": "拒绝",
        "hit_count": "50000"
    }
    
    policy = PolicyObject.from_dict(policy_data)
    
    # 打印策略的完整字符串表示（包含风险信息）
    print("策略完整信息:")
    print(policy)
    
    print("\n" + "-" * 40)
    print("详细风险报告:")
    print(policy.get_service_risk_report())


if __name__ == "__main__":
    test_service_risk_detection()
    test_service_parsing()
    test_detailed_risk_report()
