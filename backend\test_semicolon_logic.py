"""
测试分号添加逻辑
验证在不同数量的策略对象下，分号的添加是否正确
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.json_formatter import PolicyJsonFormatter
from models.policy_object import PolicyObject


def create_test_policy(policy_id: str, name: str) -> dict:
    """创建测试策略数据"""
    return {
        "id": policy_id,
        "name": name,
        "src_addr": [f"192.168.{policy_id}.0/24"],
        "dst_addr": ["any"]
    }


def test_semicolon_logic():
    """测试分号添加逻辑"""
    
    print("🧪 测试分号添加逻辑")
    print("=" * 80)
    
    # 测试不同数量的策略对象
    test_cases = [
        (1, "1个策略对象"),
        (3, "3个策略对象"),
        (5, "5个策略对象（正好一组）"),
        (7, "7个策略对象（1组+2个）"),
        (10, "10个策略对象（正好两组）"),
        (12, "12个策略对象（2组+2个）"),
        (15, "15个策略对象（正好三组）"),
        (17, "17个策略对象（3组+2个）"),
    ]
    
    for count, description in test_cases:
        print(f"\n📋 测试场景: {description}")
        print("-" * 60)
        
        # 创建测试数据
        test_policies = []
        for i in range(1, count + 1):
            # 确保策略名称符合过滤条件（包含4个数字）
            policy_name = f"测试策略-{i:04d}-长期"
            test_policies.append(create_test_policy(str(i).zfill(3), policy_name))
        
        try:
            # 转换为策略对象
            policy_objects = []
            for policy_data in test_policies:
                policy_obj = PolicyObject.from_dict(policy_data)
                policy_objects.append(policy_obj)
            
            # 格式化为JSON
            formatted_json = PolicyJsonFormatter.format_policy_objects_to_json(policy_objects)
            
            print(f"📊 结果:")
            print(formatted_json)
            
            # 分析分号数量
            semicolon_count = formatted_json.count(';')
            lines = formatted_json.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            # 计算预期的分号数量
            groups = (count + 4) // 5  # 向上取整
            expected_semicolons = max(0, groups - 1)  # 分组数-1
            
            print(f"📈 分析:")
            print(f"  - 策略数量: {count}")
            print(f"  - 分组数: {groups}")
            print(f"  - 预期分号数: {expected_semicolons}")
            print(f"  - 实际分号数: {semicolon_count}")
            print(f"  - 结果: {'✅ 正确' if semicolon_count == expected_semicolons else '❌ 错误'}")
            
            # 检查最后是否以逗号结尾而不是分号
            last_char = formatted_json.strip()[-1] if formatted_json.strip() else ''
            print(f"  - 最后字符: '{last_char}' {'✅ 正确' if last_char == ',' else '❌ 错误'}")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()


def test_edge_cases():
    """测试边界情况"""
    
    print(f"\n" + "="*80)
    print("🔍 测试边界情况")
    print("="*80)
    
    # 测试空列表
    print("\n📋 测试场景: 空策略列表")
    print("-" * 60)
    
    try:
        formatted_json = PolicyJsonFormatter.format_policy_objects_to_json([])
        print(f"📊 结果: '{formatted_json}'")
        print(f"📈 分析: {'✅ 正确' if formatted_json == '' else '❌ 错误'}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    # 测试被过滤掉所有策略的情况
    print("\n📋 测试场景: 所有策略都被过滤")
    print("-" * 60)
    
    try:
        # 创建不符合过滤条件的策略
        invalid_policies = [
            create_test_policy("001", "基础策略-主动防御"),  # 不符合条件
            create_test_policy("002", "测试策略-ABC"),      # 不符合条件
        ]
        
        policy_objects = []
        for policy_data in invalid_policies:
            policy_obj = PolicyObject.from_dict(policy_data)
            policy_objects.append(policy_obj)
        
        formatted_json = PolicyJsonFormatter.format_policy_objects_to_json(policy_objects)
        print(f"📊 结果: '{formatted_json}'")
        print(f"📈 分析: {'✅ 正确' if formatted_json == '' else '❌ 错误'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


def analyze_format_structure():
    """分析格式结构"""
    
    print(f"\n" + "="*80)
    print("📊 格式结构分析")
    print("="*80)
    
    # 创建12个策略进行详细分析
    test_policies = []
    for i in range(1, 13):
        policy_name = f"策略-{i:04d}-长期"
        test_policies.append(create_test_policy(str(i).zfill(3), policy_name))
    
    try:
        policy_objects = []
        for policy_data in test_policies:
            policy_obj = PolicyObject.from_dict(policy_data)
            policy_objects.append(policy_obj)
        
        formatted_json = PolicyJsonFormatter.format_policy_objects_to_json(policy_objects)
        
        print("📋 详细格式分析:")
        lines = formatted_json.split('\n')
        for i, line in enumerate(lines, 1):
            if line.strip():
                line_type = ""
                if line.strip() == ";":
                    line_type = " [分号行]"
                elif line.strip().endswith(","):
                    line_type = " [策略行]"
                
                print(f"  第{i:2d}行: {line.strip()}{line_type}")
        
        print(f"\n📈 结构总结:")
        print(f"  - 总行数: {len([line for line in lines if line.strip()])}")
        print(f"  - 策略行数: {len([line for line in lines if line.strip() and not line.strip() == ';'])}")
        print(f"  - 分号行数: {len([line for line in lines if line.strip() == ';'])}")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")


if __name__ == "__main__":
    print("🚀 开始分号逻辑测试")
    
    test_semicolon_logic()
    test_edge_cases()
    analyze_format_structure()
    
    print(f"\n" + "="*80)
    print("📊 测试总结")
    print("="*80)
    print("✅ 分号添加规则:")
    print("  - 每5个策略为一组")
    print("  - 只在分组之间添加分号")
    print("  - 最后一组不添加分号")
    print("  - 每个策略行以逗号结尾")
    print("  - 空列表返回空字符串")
