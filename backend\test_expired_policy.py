"""
测试过期策略是否会被包含在时间格式错误策略详细信息中
"""

from models.policy_object import PolicyObject
from routers.form_router import check_policy_time_validity
from models.policy_object import PolicyObjectList

def test_expired_policy_in_error_details():
    """测试过期策略是否会被包含在错误详情中"""
    
    print("🧪 测试过期策略归类到时间格式错误详情")
    print("=" * 60)
    
    # 创建一个包含过期策略的测试数据（使用您提到的实际案例）
    test_policies = [
        {
            "id": "22",
            "name": "20220409-省公司上海路数据中心机房k8s集群-边缘云地址-杨浩然-2年",
            "src_zone": "internal",
            "src_addr": ["192.168.1.0/24"],
            "dst_zone": "external",
            "dst_addr": ["any"],
            "service": ["TCP 80"],
            "action": "允许",
            "hit_count": "44"
        },
        {
            "id": "266",
            "name": "250702-盐城市县办公终端-安全缓冲区安全接入网关授权系统-杨浩然-临时",
            "src_zone": "internal",
            "src_addr": ["10.0.0.0/8"],
            "dst_zone": "external",
            "dst_addr": ["any"],
            "service": ["TCP 443"],
            "action": "允许",
            "hit_count": "50"
        }
    ]
    
    # 转换为策略对象列表
    policy_list = PolicyObjectList.from_json_array(test_policies)
    
    print(f"创建了 {len(policy_list)} 个测试策略")
    
    # 执行时间有效性检查（这会调用 print_policies_time_table）
    check_policy_time_validity(policy_list)

    print("\n" + "="*60)
    print("✅ 测试完成！")
    print("请查看上面的输出，确认:")
    print("1. 过期策略是否出现在时间格式错误详情中")
    print("2. 策略22应该显示为expired状态")
    print("3. 策略266应该显示为invalid状态（因为'临时'无法解析）")

if __name__ == "__main__":
    test_expired_policy_in_error_details()
