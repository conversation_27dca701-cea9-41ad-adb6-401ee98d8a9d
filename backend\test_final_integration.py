"""
测试最终集成
验证完整的策略申请检测和大模型分析流程
"""

import requests
import json


def test_final_integration():
    """测试最终集成"""
    
    print("🧪 测试最终集成 - 策略申请检测 + 大模型分析")
    print("=" * 80)
    
    test_data = {
        "items": [
            {"key": "申请人", "value": "张三"},
            {"key": "部门", "value": "IT部"},
            {"key": "申请原因", "value": "业务系统需要访问数据库服务器"},
            {"key": "源地址", "value": "192.168.1.100"},
            {"key": "目的地址", "value": "10.0.0.50"},
            {"key": "目的端口", "value": "3306"},
            {"key": "协议", "value": "TCP"},
            {"key": "业务描述", "value": "ERP系统连接MySQL数据库"},
            {"key": "预期使用时间", "value": "长期使用"}
        ]
    }
    
    try:
        print(f"📊 测试数据:")
        for item in test_data['items']:
            print(f"  - {item['key']}: {item['value']}")
        
        url = "http://127.0.0.1:8000/policy-application/detect"
        headers = {"Content-Type": "application/json"}
        
        print(f"\n🔄 发送请求到: {url}")
        print(f"⏱️ 预计等待时间: 1-2分钟（包含大模型分析）")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=180)
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            detection_data = result.get('data', {})
            
            print(f"✅ 请求成功!")
            print(f"📋 检测结果:")
            print(f"  - 处理状态: {detection_data.get('status', 'N/A')}")
            print(f"  - 处理数量: {detection_data.get('items_processed', 0)}")
            print(f"  - 是否有风险: {detection_data.get('has_risks', False)}")
            
            # 显示提取的字段
            extracted = detection_data.get('extracted_fields', {})
            print(f"  - 提取的端口: {extracted.get('destination_port', '未找到')}")
            print(f"  - 提取的协议: {extracted.get('protocol', '未找到')}")
            
            # 显示端口风险
            risks = detection_data.get('port_risks', [])
            if risks:
                print(f"  - 发现 {len(risks)} 个风险:")
                for j, risk in enumerate(risks, 1):
                    print(f"    {j}. {risk['protocol']}:{risk['port']} - {risk['risk_level']}")
                    print(f"       {risk['reason']}")
            else:
                print(f"  - 未发现端口风险")
            
            # 显示大模型分析结果
            llm_analysis = detection_data.get('llm_analysis', '')
            if llm_analysis and "大模型分析暂时不可用" not in llm_analysis:
                print(f"\n🤖 大模型分析结果:")
                print("-" * 60)
                print(llm_analysis)
                print("-" * 60)
                print(f"✅ 大模型分析成功! 响应长度: {len(llm_analysis)} 字符")
                return True
            else:
                print(f"\n❌ 大模型分析失败:")
                print(f"   {llm_analysis}")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保后端服务已启动")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时: 大模型分析时间过长")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 开始最终集成测试")
    
    success = test_final_integration()
    
    print(f"\n" + "="*80)
    print("📊 测试总结")
    print("="*80)
    
    if success:
        print("🎉 最终集成测试成功!")
        print("\n✅ 功能验证:")
        print("  - ✅ 端口风险检测正常")
        print("  - ✅ 大模型分析正常")
        print("  - ✅ API响应格式正确")
        print("  - ✅ 前端可以正常显示结果")
        
        print(f"\n📋 完整功能流程:")
        print(f"  1. 前端提交策略申请键值对")
        print(f"  2. 后端提取目的端口和协议")
        print(f"  3. 进行端口风险检测")
        print(f"  4. 调用RAGFlow大模型分析")
        print(f"  5. 返回包含风险检测和智能分析的完整结果")
        print(f"  6. 前端显示端口风险和智能建议")
    else:
        print("❌ 最终集成测试失败")
        print("请检查:")
        print("  - 后端服务是否启动")
        print("  - RAGFlow服务是否可访问")
        print("  - 网络连接是否正常")
    
    print(f"\n🚀 启动后端服务命令:")
    print(f"  cd backend")
    print(f"  python -m uvicorn main:app --reload")
