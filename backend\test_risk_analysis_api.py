"""
测试风险分析API接口
"""

import asyncio
import json
from routers.form_router import get_risk_analysis


async def test_risk_analysis_api():
    """测试风险分析API接口"""
    
    print("🧪 测试风险分析API接口")
    print("=" * 80)
    
    # 模拟包含各种风险级别的策略数据
    test_request = {
        "type": "policy_data",
        "count": 4,
        "data": [
            {
                "id": "HIGH_001",
                "name": "高风险策略-木马端口访问",
                "src_zone": "external",
                "src_addr": ["any"],
                "dst_zone": "internal",
                "dst_addr": ["192.168.1.0/24"],
                "service": ["TCP 0", "UDP 31337", "TCP 1-10"],  # 无效端口, Back Orifice, 小范围包含风险端口
                "action": "允许",
                "hit_count": "5000"
            },
            {
                "id": "HIGH_002", 
                "name": "高风险策略-大范围端口",
                "src_zone": "dmz",
                "src_addr": ["172.16.0.0/16"],
                "dst_zone": "internal",
                "dst_addr": ["10.0.0.0/8"],
                "service": ["TCP 135-139", "UDP 50000-60000"],  # Windows网络端口, 大范围UDP
                "action": "拒绝",
                "hit_count": "12000"
            },
            {
                "id": "MED_001",
                "name": "中风险策略-受控服务",
                "src_zone": "internal",
                "src_addr": ["192.168.0.0/16"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["TCP 21", "TCP 22", "TCP 23", "TCP 443"],  # FTP, SSH, Telnet, HTTPS
                "action": "允许",
                "hit_count": "8000"
            },
            {
                "id": "SAFE_001",
                "name": "安全策略-常用端口",
                "src_zone": "internal",
                "src_addr": ["10.10.10.0/24"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["ICMP", "TCP 8080", "UDP 12345"],  # 安全协议和端口
                "action": "允许",
                "hit_count": "3000"
            }
        ]
    }
    
    print("📊 测试数据包含:")
    print("  - 2个高风险策略 (木马端口, 大范围端口)")
    print("  - 1个中风险策略 (受控使用端口)")
    print("  - 1个安全策略 (常用安全端口)")
    print()
    
    # 调用风险分析接口
    try:
        result = await get_risk_analysis(test_request)
        
        print("✅ API调用成功")
        print(f"返回状态码: {result['code']}")
        print(f"返回消息: {result['msg']}")
        print()
        
        if result['code'] == 200:
            data = result['data']
            
            # 打印统计信息
            stats = data['statistics']
            print("📊 风险统计:")
            print(f"  总策略数: {stats['total_policies']}")
            print(f"  高风险策略: {stats['high_risk_count']}")
            print(f"  中风险策略: {stats['medium_risk_count']}")
            print(f"  安全策略: {stats['safe_count']}")
            print()
            
            # 打印高风险策略详情
            if data['high_risk_policies']:
                print("🔴 高风险策略详情:")
                print("-" * 60)
                for policy in data['high_risk_policies']:
                    print(f"\n【高风险策略 {policy['index']}】")
                    print(f"策略ID: {policy['policy_id']}")
                    print(f"策略名称: {policy['policy_name']}")
                    print(f"禁止使用服务列表:")
                    
                    for service in policy['prohibited_services']:
                        print(f"  服务: {service['service']} (风险级别: {service['risk_level']})")
                        
                        if service.get('ports'):
                            for port in service['ports']:
                                print(f"    端口 {port['port']}: {port['category']}")
                                if port['risk']:
                                    print(f"      风险: {port['risk']}")
                        
                        if service.get('general_risk'):
                            print(f"    风险: {service['general_risk']}")
                        
                        if service.get('additional_ports_count'):
                            print(f"    ... 还有 {service['additional_ports_count']} 个端口的详细信息")
                    
                    print(f"风险摘要: {policy['risk_summary']}")
            
            # 打印中风险策略详情
            if data['medium_risk_policies']:
                print(f"\n🟡 中风险策略详情:")
                print("-" * 60)
                for policy in data['medium_risk_policies']:
                    print(f"\n【中风险策略 {policy['index']}】")
                    print(f"策略ID: {policy['policy_id']}")
                    print(f"策略名称: {policy['policy_name']}")
                    print(f"受控使用服务列表:")
                    
                    for service in policy['controlled_services']:
                        print(f"  服务: {service['service']}")
                        
                        if service.get('ports'):
                            for port in service['ports']:
                                print(f"    端口 {port['port']}: {port['category']}")
                                if port['risk']:
                                    print(f"      风险: {port['risk']}")
                        
                        if service.get('general_risk'):
                            print(f"    风险: {service['general_risk']}")
                        
                        if service.get('additional_ports_count'):
                            print(f"    ... 还有 {service['additional_ports_count']} 个端口的详细信息")
            
            # 打印安全策略统计
            if data['safe_policies_count'] > 0:
                print(f"\n🟢 安全策略: {data['safe_policies_count']} 个策略未发现危险端口")
            
            # 打印完整的JSON结构（用于调试）
            print(f"\n📋 完整JSON结构:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
        
        else:
            print(f"❌ API调用失败: {result}")
    
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_error_cases():
    """测试错误情况"""
    
    print(f"\n" + "="*80)
    print("🔍 测试错误情况")
    print("="*80)
    
    # 测试无效数据类型
    invalid_request = {
        "type": "form_data",
        "items": [{"key": "test", "value": "test"}]
    }
    
    print("测试1: 无效数据类型")
    result = await get_risk_analysis(invalid_request)
    print(f"结果: {result}")
    print()
    
    # 测试无效策略数据
    invalid_policy_request = {
        "type": "policy_data",
        "count": 1,
        "data": "invalid_data"  # 应该是列表
    }
    
    print("测试2: 无效策略数据格式")
    result = await get_risk_analysis(invalid_policy_request)
    print(f"结果: {result}")
    print()
    
    # 测试空数据
    empty_request = {
        "type": "policy_data",
        "count": 0,
        "data": []
    }
    
    print("测试3: 空策略数据")
    result = await get_risk_analysis(empty_request)
    print(f"结果: {result}")


if __name__ == "__main__":
    asyncio.run(test_risk_analysis_api())
    asyncio.run(test_error_cases())
