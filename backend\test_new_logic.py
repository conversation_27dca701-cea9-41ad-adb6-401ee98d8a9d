"""
测试新的端口风险检测逻辑
验证TCP和UDP的不同处理策略
"""

import requests
import json


def test_tcp_large_range():
    """测试TCP大范围端口"""
    
    print("🧪 测试TCP大范围端口(8000-20000)")
    print("=" * 80)
    
    test_data = {
        "items": [
            {"key": "申请人", "value": "测试用户"},
            {"key": "目的端口", "value": "8000-20000"},
            {"key": "协议", "value": "TCP"}
        ]
    }
    
    send_request_and_analyze(test_data)


def test_udp_large_range():
    """测试UDP大范围端口"""
    
    print(f"\n" + "="*80)
    print("🧪 测试UDP大范围端口(12000-55000)")
    print("=" * 80)
    
    test_data = {
        "items": [
            {"key": "申请人", "value": "测试用户"},
            {"key": "目的端口", "value": "12000-55000"},
            {"key": "协议", "value": "UDP"}
        ]
    }
    
    send_request_and_analyze(test_data)


def test_udp_specific_ports():
    """测试UDP特定高风险端口"""
    
    print(f"\n" + "="*80)
    print("🧪 测试UDP特定高风险端口")
    print("=" * 80)
    
    test_data = {
        "items": [
            {"key": "申请人", "value": "测试用户"},
            {"key": "目的端口", "value": "54321、31337、12345"},
            {"key": "协议", "value": "UDP"}
        ]
    }
    
    send_request_and_analyze(test_data)


def send_request_and_analyze(test_data):
    """发送请求并分析结果"""
    
    try:
        print(f"📊 测试数据:")
        for item in test_data['items']:
            print(f"  - {item['key']}: {item['value']}")
        
        url = "http://127.0.0.1:8000/policy-application/detect"
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            detection_data = result.get('data', {})
            
            print(f"✅ 请求成功!")
            print(f"📋 检测结果:")
            print(f"  - 处理状态: {detection_data.get('status', 'N/A')}")
            print(f"  - 是否有风险: {detection_data.get('has_risks', False)}")
            
            extracted = detection_data.get('extracted_fields', {})
            print(f"  - 提取的端口: {extracted.get('destination_port', '未找到')}")
            print(f"  - 提取的协议: {extracted.get('protocol', '未找到')}")
            
            risks = detection_data.get('port_risks', [])
            if risks:
                print(f"  - 发现 {len(risks)} 个风险/提示:")
                for j, risk in enumerate(risks, 1):
                    risk_type = ""
                    if risk.get('is_notice'):
                        risk_type = " [提示]"
                    
                    print(f"    {j}. {risk['protocol']}:{risk['port']} - {risk['risk_level']}{risk_type}")
                    print(f"       {risk['reason']}")
            else:
                print(f"  - 未发现端口风险")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保后端服务已启动")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


if __name__ == "__main__":
    print("🚀 测试新的端口风险检测逻辑")
    print("\n📋 逻辑说明:")
    print("  1. TCP端口: 范围内所有端口逐一查询哈希表")
    print("  2. UDP端口: 1万以内逐一查询，1万以上只检查特定高风险端口")
    print("  3. UDP高风险端口: 54321、47262、31789、31339、31338、31337、26274、21554、19132、12345、10167、10067")
    
    test_tcp_large_range()
    test_udp_large_range()
    test_udp_specific_ports()
