"""
JSON格式化工具
用于将策略对象转换为指定格式的JSON字符串
"""

import json
import re
from typing import List
from models.policy_object import PolicyObject


class PolicyJsonFormatter:
    """策略JSON格式化器"""

    @staticmethod
    def _is_valid_policy_name(policy_name: str) -> bool:
        """
        检查策略名称是否符合过滤条件

        条件：策略名称中必须存在连续的4个数字或6个数字或8个数字，或者包含"长期"这两个字

        Args:
            policy_name: 策略名称

        Returns:
            bool: True表示符合条件，False表示不符合条件
        """
        if not policy_name:
            return False

        # 检查是否包含"长期"
        if "长期" in policy_name:
            return True

        # 检查是否包含连续的4个、6个或8个数字
        # 使用正则表达式匹配连续的数字
        digit_patterns = [
            r'\d{4}',  # 连续4个数字
            r'\d{6}',  # 连续6个数字
            r'\d{8}'   # 连续8个数字
        ]

        for pattern in digit_patterns:
            if re.search(pattern, policy_name):
                return True

        return False

    @staticmethod
    def format_policy_objects_to_json(policy_objects: List[PolicyObject]) -> str:
        """
        将策略对象列表格式化为指定的JSON字符串格式
        
        Args:
            policy_objects: 策略对象列表
            
        Returns:
            str: 格式化后的JSON字符串
        """
        if not policy_objects:
            return ""
        
        # 1. 构造JSON，修改键名为中文，只包含id、策略名称、源地址、目的地址这4个字段
        # 同时过滤不符合条件的策略对象
        json_objects = []
        filtered_count = 0

        for policy_obj in policy_objects:
            # 检查策略名称是否符合过滤条件
            if not PolicyJsonFormatter._is_valid_policy_name(policy_obj.name):
                filtered_count += 1
                print(f"⚠️ 过滤策略: ID={policy_obj.id}, 名称='{policy_obj.name}' (不符合条件)")
                continue

            json_obj = {
                "id": policy_obj.id,
                "策略名称": policy_obj.name,
                "源地址": policy_obj.src_addr,
                "目的地址": policy_obj.dst_addr
            }
            json_objects.append(json_obj)

        # 打印过滤统计信息
        if filtered_count > 0:
            print(f"📊 过滤统计: 总策略数={len(policy_objects)}, 过滤掉={filtered_count}, 保留={len(json_objects)}")
        else:
            print(f"📊 过滤统计: 总策略数={len(policy_objects)}, 全部保留={len(json_objects)}")
        
        # 2. 每5个JSON对象之间加个英文分号，不使用中括号包裹
        json_parts = []
        for i in range(0, len(json_objects), 5):
            batch = json_objects[i:i+5]
            # 将每个对象转换为单行JSON字符串
            batch_lines = []
            for obj in batch:
                obj_json = json.dumps(obj, ensure_ascii=False, separators=(',', ': '))
                batch_lines.append(f" {obj_json}")
            
            # 用逗号和换行连接同一批次的对象
            batch_str = ",\n".join(batch_lines)
            json_parts.append(batch_str)
        
        # 3. 将最终构造的JSON，只在分组之间加分号，最后一组不加分号
        if not json_parts:
            return ""

        if len(json_parts) == 1:
            # 只有一组数据，不需要分号
            final_json = json_parts[0] + ","
        else:
            # 多组数据，在前面的分组后加分号，最后一组不加分号
            final_json_parts = []
            for i, part in enumerate(json_parts):
                if i < len(json_parts) - 1:
                    # 不是最后一组，添加分号
                    final_json_parts.append(part + ",\n;")
                else:
                    # 最后一组，只添加逗号
                    final_json_parts.append(part + ",")

            final_json = "\n".join(final_json_parts)

        return final_json
    
    @staticmethod
    def parse_and_format_policy_data(policy_data: List[dict]) -> tuple[List[PolicyObject], str]:
        """
        解析策略数据并格式化为JSON字符串
        
        Args:
            policy_data: 原始策略数据列表
            
        Returns:
            tuple: (策略对象列表, 格式化的JSON字符串)
        """
        # 1. 将前端发来的数据解析为对象数组
        policy_objects = []
        for item in policy_data:
            try:
                policy_obj = PolicyObject.from_dict(item)
                policy_objects.append(policy_obj)
            except Exception as e:
                print(f"⚠️ 解析策略对象失败: {str(e)}, 数据: {item}")
                continue
        
        print(f"✅ 成功解析 {len(policy_objects)} 个策略对象")
        
        # 2. 格式化为JSON字符串
        formatted_json = PolicyJsonFormatter.format_policy_objects_to_json(policy_objects)
        
        return policy_objects, formatted_json
    
    @staticmethod
    def print_formatted_json(formatted_json: str, title: str = "构造的JSON结果"):
        """
        打印格式化的JSON结果
        
        Args:
            formatted_json: 格式化的JSON字符串
            title: 打印标题
        """
        print(f"\n📋 {title}:")
        print("=" * 80)
        print(formatted_json)
        print("=" * 80)
