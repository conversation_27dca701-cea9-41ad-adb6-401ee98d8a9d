<script lang="ts" setup>
import { ref } from 'vue';
import HomePage from '@/components/HomePage.vue';
import ScreenCapture from '@/components/ScreenCapture.vue';
import DomCapture from '@/components/DomCapture.vue';

// 当前页面
const currentPage = ref('home');

// 导航到指定页面
function navigateTo(page: string) {
  currentPage.value = page;
}

// 返回主页
function goToHome() {
  currentPage.value = 'home';
}
</script>

<template>
  <div class="app-container">
    <div class="header">
      <img src="/wxt.svg" class="logo" alt="WXT logo" />
      <h1>信息获取助手</h1>
    </div>
    
    <!-- 根据当前页面显示不同的组件 -->
    <div class="content">
      <HomePage 
        v-if="currentPage === 'home'" 
        @navigate="navigateTo" 
      />
      
      <ScreenCapture 
        v-else-if="currentPage === 'screenshot'" 
        @back="goToHome" 
      />
      
      <DomCapture 
        v-else-if="currentPage === 'dom'" 
        @back="goToHome" 
      />
    </div>
  </div>
</template>

<style scoped>
.app-container {
  font-family: Arial, sans-serif;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.logo {
  height: 1.8em;
  margin-right: 0.5rem;
}

h1 {
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* 自定义滚动条样式 */
.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
