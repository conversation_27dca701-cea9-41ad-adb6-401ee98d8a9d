"""
端口风险数据处理器
读取xlsx文档并构建端口风险哈希表
"""

import pandas as pd
import os
from typing import Dict, List, Tuple, Any


class PortRiskProcessor:
    """端口风险数据处理器"""
    
    def __init__(self, xlsx_file_path: str = "ports.xlsx"):
        """
        初始化处理器
        
        Args:
            xlsx_file_path: xlsx文件路径
        """
        self.xlsx_file_path = xlsx_file_path
        self.port_risk_map: Dict[str, str] = {}
    
    def read_xlsx_data(self) -> List[Dict[str, Any]]:
        """
        读取xlsx文件数据
        
        Returns:
            List[Dict]: 端口数据列表
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(self.xlsx_file_path):
                raise FileNotFoundError(f"文件不存在: {self.xlsx_file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(self.xlsx_file_path)
            
            print(f"✅ 成功读取Excel文件: {self.xlsx_file_path}")
            print(f"📊 数据行数: {len(df)}")
            print(f"📋 列名: {list(df.columns)}")

            # 显示前几行数据用于调试
            print(f"🔍 前5行数据预览:")
            for i, row in df.head().iterrows():
                print(f"  行{i+1}: {dict(row)}")

            # 转换为字典列表
            data_list = df.to_dict('records')
            
            return data_list
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {str(e)}")
            return []
    
    def process_port_data(self, data_list: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        处理端口数据，构建哈希表
        
        Args:
            data_list: 端口数据列表
            
        Returns:
            Dict[str, str]: 端口风险哈希表 {协议+端口号: 分类+风险}
        """
        port_risk_map = {}
        
        print(f"\n🔄 开始处理端口数据...")
        
        for i, row in enumerate(data_list, 1):
            try:
                # 提取字段（处理可能的NaN值）
                port_num = str(row.get('端口号', '')).strip()
                protocol = str(row.get('协议', '')).strip()
                risk = str(row.get('风险', '')).strip()
                category = str(row.get('分类', '')).strip()
                
                # 跳过无效数据
                if not port_num or port_num == 'nan' or not protocol or protocol == 'nan':
                    print(f"⚠️  跳过第{i}行: 端口号或协议为空")
                    continue
                
                # 处理协议类型
                protocols = self._parse_protocol(protocol)
                
                # 构建值（分类 + 风险）
                value = self._build_value(category, risk)
                
                # 为每个协议创建键值对
                for proto in protocols:
                    key = f"{proto}:{port_num}"  # 使用冒号分隔协议和端口
                    port_risk_map[key] = value
                    
                if i <= 5:  # 只显示前5条的详细信息
                    print(f"  处理第{i}行: 端口{port_num}, 协议{protocols}, 值: {value}")
                
            except Exception as e:
                print(f"❌ 处理第{i}行数据时出错: {str(e)}")
                continue
        
        print(f"✅ 处理完成，共生成 {len(port_risk_map)} 个键值对")
        return port_risk_map
    
    def _parse_protocol(self, protocol: str) -> List[str]:
        """
        解析协议字符串
        
        Args:
            protocol: 协议字符串，如 "TCP", "UDP", "TCP/UDP"
            
        Returns:
            List[str]: 协议列表
        """
        protocol = protocol.upper().strip()
        
        if '/' in protocol:
            # 处理 "TCP/UDP" 格式
            return [p.strip() for p in protocol.split('/') if p.strip()]
        else:
            # 单一协议
            return [protocol]
    
    def _build_value(self, category: str, risk: str) -> str:
        """
        构建哈希表的值

        Args:
            category: 分类
            risk: 风险

        Returns:
            str: 组合后的值
        """
        # 处理NaN值
        category = category if category != 'nan' else ''
        risk = risk if risk != 'nan' else ''

        # 组合分类和风险，使用管道符分隔
        if category and risk:
            return f"{category}|{risk}"
        elif category:
            return category
        elif risk:
            return risk
        else:
            return "未知"
    
    def generate_hash_table_string(self, port_risk_map: Dict[str, str]) -> str:
        """
        生成哈希表的字符串表示，方便打表使用
        
        Args:
            port_risk_map: 端口风险哈希表
            
        Returns:
            str: 哈希表字符串
        """
        print(f"\n📝 生成哈希表字符串...")
        
        # 按键排序
        sorted_items = sorted(port_risk_map.items())
        
        # 生成不同格式的字符串
        formats = {
            'python_dict': self._generate_python_dict(sorted_items),
            'java_map': self._generate_java_map(sorted_items),
            'json_format': self._generate_json_format(sorted_items),
            'csv_format': self._generate_csv_format(sorted_items)
        }
        
        return formats
    
    def _generate_python_dict(self, sorted_items: List[Tuple[str, str]]) -> str:
        """生成Python字典格式"""
        lines = ["# Python字典格式", "PORT_RISK_MAP = {"]

        for key, value in sorted_items:
            # 转义字符串中的特殊字符
            escaped_key = key.replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
            escaped_value = value.replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
            lines.append(f'    "{escaped_key}": "{escaped_value}",')

        lines.append("}")
        return '\n'.join(lines)
    
    def _generate_java_map(self, sorted_items: List[Tuple[str, str]]) -> str:
        """生成Java Map格式"""
        lines = [
            "// Java Map格式",
            "Map<String, String> portRiskMap = new HashMap<>();"
        ]
        
        for key, value in sorted_items:
            lines.append(f'portRiskMap.put("{key}", "{value}");')
        
        return '\n'.join(lines)
    
    def _generate_json_format(self, sorted_items: List[Tuple[str, str]]) -> str:
        """生成JSON格式"""
        import json
        
        data = dict(sorted_items)
        return "// JSON格式\n" + json.dumps(data, ensure_ascii=False, indent=2)
    
    def _generate_csv_format(self, sorted_items: List[Tuple[str, str]]) -> str:
        """生成CSV格式"""
        lines = ["# CSV格式", "协议端口,分类风险"]
        
        for key, value in sorted_items:
            lines.append(f"{key},{value}")
        
        return '\n'.join(lines)
    
    def save_hash_table_to_file(self, formats: Dict[str, str], output_dir: str = "."):
        """
        保存哈希表到文件
        
        Args:
            formats: 不同格式的哈希表字符串
            output_dir: 输出目录
        """
        print(f"\n💾 保存哈希表到文件...")
        
        file_extensions = {
            'python_dict': '.py',
            'java_map': '.java',
            'json_format': '.json',
            'csv_format': '.csv'
        }
        
        for format_name, content in formats.items():
            filename = f"port_risk_map{file_extensions.get(format_name, '.txt')}"
            filepath = os.path.join(output_dir, filename)
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ 保存 {format_name} 格式到: {filepath}")
            except Exception as e:
                print(f"  ❌ 保存 {format_name} 格式失败: {str(e)}")
    
    def process_and_generate(self) -> Dict[str, str]:
        """
        完整的处理流程：读取 -> 处理 -> 生成
        
        Returns:
            Dict[str, str]: 不同格式的哈希表字符串
        """
        print(f"🚀 开始处理端口风险数据...")
        print("=" * 60)
        
        # 1. 读取Excel数据
        data_list = self.read_xlsx_data()
        if not data_list:
            print("❌ 无法读取数据，处理终止")
            return {}
        
        # 2. 处理数据构建哈希表
        self.port_risk_map = self.process_port_data(data_list)
        if not self.port_risk_map:
            print("❌ 无法构建哈希表，处理终止")
            return {}
        
        # 3. 生成字符串格式
        formats = self.generate_hash_table_string(self.port_risk_map)
        
        # 4. 保存到文件
        self.save_hash_table_to_file(formats)
        
        # 5. 打印统计信息
        self.print_statistics()
        
        print("=" * 60)
        print("🎉 处理完成！")
        
        return formats
    
    def print_statistics(self):
        """打印统计信息"""
        print(f"\n📊 统计信息:")
        print(f"  总键值对数量: {len(self.port_risk_map)}")
        
        # 按协议统计
        protocol_stats = {}
        for key in self.port_risk_map.keys():
            protocol = key.split(':')[0]  # 使用冒号分隔
            protocol_stats[protocol] = protocol_stats.get(protocol, 0) + 1
        
        print(f"  协议分布:")
        for protocol, count in sorted(protocol_stats.items()):
            print(f"    {protocol}: {count} 个端口")
        
        # 按分类统计
        category_stats = {}
        for value in self.port_risk_map.values():
            category = value.split('|')[0] if '|' in value else value  # 使用管道符分隔
            category_stats[category] = category_stats.get(category, 0) + 1
        
        print(f"  分类分布:")
        for category, count in sorted(category_stats.items()):
            print(f"    {category}: {count} 个端口")


def main():
    """主函数"""
    # 创建处理器
    processor = PortRiskProcessor("ports.xlsx")
    
    # 执行完整处理流程
    formats = processor.process_and_generate()
    
    # 打印Python字典格式（用于直接复制使用）
    if 'python_dict' in formats:
        print(f"\n" + "="*60)
        print("📋 Python字典格式（可直接复制使用）:")
        print("="*60)
        print(formats['python_dict'])


if __name__ == "__main__":
    main()
