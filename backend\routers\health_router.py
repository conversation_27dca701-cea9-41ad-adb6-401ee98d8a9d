"""
健康检查和测试相关路由
"""

import logging
from fastapi import APIRouter
from fastapi.responses import JSONResponse

from services.ragflow_service import default_service as rag_service
from config import config

logger = logging.getLogger(__name__)
router = APIRouter(tags=["健康检查"])


@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "服务正常运行"}


@router.get("/ragflow/test")
async def test_ragflow_connection():
    """测试RAGFlow连接"""
    try:
        # 检查RAGFlow服务是否可用
        if not rag_service._rag_client:
            return JSONResponse(
                status_code=503,
                content={
                    "code": 503,
                    "data": None,
                    "msg": "RAGFlow服务不可用，请检查配置和网络连接"
                }
            )

        agents = rag_service._rag_client.list_agents()
        target_agent = None
        for agent in agents:
            if agent.id == config.RAGFLOW_ASSISTANT_ID:
                target_agent = agent
                break
        
        if target_agent:
            return JSONResponse(content={
                "code": 200,
                "data": {
                    "agent_id": target_agent.id,
                    "agent_name": target_agent.name,
                    "status": "connected"
                },
                "msg": "RAGFlow连接成功"
            })
        else:
            return JSONResponse(content={
                "code": 404,
                "data": None,
                "msg": f"未找到指定的智能体ID: {config.RAGFLOW_ASSISTANT_ID}"
            })
            
    except Exception as e:
        logger.error(f"RAGFlow连接测试失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "data": None,
                "msg": f"RAGFlow连接失败: {str(e)}"
            }
        )
