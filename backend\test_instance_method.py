"""
测试实例方法是否正确
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_instance_method():
    """测试实例方法"""
    
    print("🧪 测试RAGFlowService实例方法")
    print("=" * 80)
    
    try:
        from services.ragflow_service import RAGFlowService, Config, policy_analysis_service
        
        print("✅ 导入成功")
        
        # 检查类定义
        print(f"\n📋 RAGFlowService类方法:")
        methods = [method for method in dir(RAGFlowService) if not method.startswith('_')]
        for method in methods:
            print(f"  - {method}")
        
        # 检查实例方法
        print(f"\n📋 policy_analysis_service实例方法:")
        instance_methods = [method for method in dir(policy_analysis_service) if not method.startswith('_')]
        for method in instance_methods:
            print(f"  - {method}")
        
        # 直接检查方法存在性
        if hasattr(policy_analysis_service, 'process_formatted_json'):
            print(f"\n✅ policy_analysis_service.process_formatted_json 方法存在")
            
            # 检查方法类型
            method = getattr(policy_analysis_service, 'process_formatted_json')
            print(f"📋 方法类型: {type(method)}")
            print(f"📋 方法文档: {method.__doc__[:100] if method.__doc__ else 'None'}...")
            
        else:
            print(f"\n❌ policy_analysis_service.process_formatted_json 方法不存在")
        
        # 测试调用
        print(f"\n🔄 测试方法调用...")
        test_json = '{"test": "data"}'
        
        try:
            result = policy_analysis_service.process_formatted_json(test_json)
            print(f"✅ 调用成功，结果类型: {type(result)}")
        except Exception as e:
            print(f"⚠️ 调用失败（预期的）: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_instance_method()
