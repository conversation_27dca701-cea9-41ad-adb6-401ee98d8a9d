"""
测试RAGFlow修复
"""

def test_ragflow_fix():
    """测试RAGFlow修复"""
    
    print("🧪 测试RAGFlow修复")
    print("=" * 80)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from services.ragflow_service import analyze_policy_application_data
        
        # 测试数据
        test_details = """申请人: 张三
部门: IT部
申请原因: 业务系统需要访问数据库服务器
目的端口: 3306
协议: TCP"""
        
        print(f"📊 测试数据:")
        print(test_details)
        
        print(f"\n🔄 开始调用大模型分析...")
        
        result = analyze_policy_application_data(test_details)
        
        print(f"✅ 大模型分析成功!")
        print(f"📋 分析结果长度: {len(result)} 字符")
        print(f"📋 分析结果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ 大模型分析失败: {str(e)}")
        return False


if __name__ == "__main__":
    test_ragflow_fix()
