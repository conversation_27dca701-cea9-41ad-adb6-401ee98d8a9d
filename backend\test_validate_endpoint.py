"""
测试 /validate 接口的新功能
验证数据解析和JSON构造功能
"""

import asyncio
import json
from routers.form_router import handle_form_validation


async def test_validate_endpoint():
    """测试 /validate 接口"""
    
    print("🧪 测试 /validate 接口的新功能")
    print("=" * 80)
    
    # 模拟前端发送的策略数据
    test_request = {
        "type": "policy_data",
        "count": 12,
        "data": [
            {
                "id": "001",
                "name": "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
                "src_zone": "internal",
                "src_addr": ["192.168.1.0/24", "192.168.2.0/24"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["TCP 80", "TCP 443"],
                "action": "允许",
                "hit_count": "1500"
            },
            {
                "id": "002", 
                "name": "基础策略-主动防御封禁-IN",
                "src_zone": "any",
                "src_addr": ["any"],
                "dst_zone": "internal",
                "dst_addr": ["10.0.0.0/8"],
                "service": ["TCP 135", "TCP 139"],
                "action": "拒绝",
                "hit_count": "50000"
            },
            {
                "id": "003",
                "name": "240816-省内地址-产业园微碳慧能主题网站",
                "src_zone": "dmz",
                "src_addr": ["172.16.0.0/16"],
                "dst_zone": "external", 
                "dst_addr": ["203.0.113.0/24", "198.51.100.0/24"],
                "service": ["TCP 443", "TCP 80"],
                "action": "允许",
                "hit_count": "2000"
            },
            {
                "id": "004",
                "name": "应急指挥系统基准策略勿动-20240507-长期",
                "src_zone": "management",
                "src_addr": ["10.10.10.0/24"],
                "dst_zone": "any",
                "dst_addr": ["any"],
                "service": ["UDP 53", "TCP 22"],
                "action": "允许",
                "hit_count": "800"
            },
            {
                "id": "005",
                "name": "20231122-省侧work节点服务-边缘云-ldh-长期",
                "src_zone": "cloud",
                "src_addr": ["203.0.113.0/24"],
                "dst_zone": "internal",
                "dst_addr": ["192.168.0.0/16"],
                "service": ["ICMP", "TCP 8080"],
                "action": "允许",
                "hit_count": "300"
            },
            {
                "id": "006",
                "name": "测试策略-办公网络访问",
                "src_zone": "office",
                "src_addr": ["192.168.100.0/24"],
                "dst_zone": "internet",
                "dst_addr": ["any"],
                "service": ["TCP 80", "TCP 443", "UDP 53"],
                "action": "允许",
                "hit_count": "1200"
            },
            {
                "id": "007",
                "name": "数据库访问策略-生产环境",
                "src_zone": "app_server",
                "src_addr": ["10.1.1.0/24"],
                "dst_zone": "database",
                "dst_addr": ["10.2.1.10", "10.2.1.11"],
                "service": ["TCP 3306", "TCP 5432"],
                "action": "允许",
                "hit_count": "5000"
            },
            {
                "id": "008",
                "name": "监控系统策略-SNMP访问",
                "src_zone": "monitor",
                "src_addr": ["10.3.1.100"],
                "dst_zone": "network_device",
                "dst_addr": ["10.4.0.0/16"],
                "service": ["UDP 161", "UDP 162"],
                "action": "允许",
                "hit_count": "800"
            },
            {
                "id": "009",
                "name": "邮件服务器策略-SMTP访问",
                "src_zone": "mail_server",
                "src_addr": ["10.5.1.10"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["TCP 25", "TCP 587", "TCP 465"],
                "action": "允许",
                "hit_count": "2500"
            },
            {
                "id": "010",
                "name": "VPN访问策略-远程办公",
                "src_zone": "vpn_client",
                "src_addr": ["192.168.200.0/24"],
                "dst_zone": "internal",
                "dst_addr": ["10.0.0.0/8"],
                "service": ["any"],
                "action": "允许",
                "hit_count": "1800"
            },
            {
                "id": "011",
                "name": "备份系统策略-数据同步",
                "src_zone": "backup_server",
                "src_addr": ["10.6.1.20"],
                "dst_zone": "storage",
                "dst_addr": ["10.7.1.0/24"],
                "service": ["TCP 22", "TCP 873"],
                "action": "允许",
                "hit_count": "600"
            },
            {
                "id": "012",
                "name": "安全扫描策略-漏洞检测",
                "src_zone": "security_scanner",
                "src_addr": ["10.8.1.50"],
                "dst_zone": "all_zones",
                "dst_addr": ["any"],
                "service": ["TCP 1-65535"],
                "action": "允许",
                "hit_count": "10000"
            }
        ]
    }
    
    print("📊 测试数据包含:")
    print(f"  - 策略数量: {len(test_request['data'])}")
    print("  - 预期结果: 每5个策略为一组，用分号分隔")
    print("  - 只包含字段: id, name, src_addr, dst_addr")
    print()
    
    # 调用处理函数
    try:
        result = await handle_form_validation(test_request)
        print(f"\n✅ 接口调用成功")
        print(f"返回状态码: {result['code']}")
        print(f"返回消息: {result['msg']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_validate_endpoint())
