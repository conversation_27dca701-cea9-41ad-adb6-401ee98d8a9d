---
description: 
globs: 
alwaysApply: false
---
---
description: This document outlines comprehensive guidelines and best practices for developing browser extensions using the WXT framework. It covers project structure, coding standards, permission management, communication patterns, UI design principles, browser compatibility considerations, and security requirements. Following these guidelines will ensure consistent, maintainable, and high-quality extensions that provide excellent user experiences across different browsers and platforms.
globs: 
alwaysApply: false
---
- 
  Basic Architecture

  ### Project Structure

  - All entry points must be placed in the entrypoints/ directory

  - Shared components go in the components/ directory

  - Static assets go in the public/ directory

  - Assets that need processing (SVGs, images, etc.) go in the assets/ directory

  ### Configuration Files

  - Main configuration file is wxt.config.ts, containing manifest and build configurations

  - Use the defineConfig function to define configuration

  - Declare all required permissions and host permissions in the config

  ### Entry Point Types

  - background: Background script, use defineBackground

  - popup: Popup window, use HTML entry

  - content: Content script, use defineContentScript

  - options: Options page

  - Other optional entry points like devtools, sidepanel, etc.

  ## Code Standards

  ### TypeScript Usage

  - Use TypeScript for type safety whenever possible

  - Create interfaces or types for complex data structures

  - Use Promises and async/await for asynchronous operations

  ### Vue Component Best Practices

  - Use Composition API (<script setup> syntax)

  - Use ref and reactive for state management

  - Use lifecycle hooks like onMounted

  - Components should have single responsibility and be reusable

  ### Error Handling

  - Wrap all asynchronous operations in try/catch blocks

  - Provide detailed error messages

  - Consider fallback strategies, such as degraded implementations

  - Use instanceof Error to check error types

  ### Browser APIs

  - Use browser.* APIs instead of chrome.* (WXT will handle this uniformly)

  - Follow the principle of least privilege for permissions

  - Keep background and content script code inside the main() function

  ## Permission Management

  ### Principle of Least Privilege

  - Only request absolutely necessary permissions

  - Prefer activeTab over tabs when possible

  - Avoid using <all_urls> host permission if possible

  ### Common Permissions

  - activeTab: Interactive access to the current tab

  - tabs: Access tab information

  - storage: Store data

  - windows: Window operations

  - scripting: Execute scripts

  ### Manifest Configuration

  - Configure the manifest section in wxt.config.ts

  - Specify extension name, description, icons

  - Define permissions, host permissions

  - Set action properties (like default_title)

  ## Message Communication

  ### Communication Between Contexts

  - Use browser.runtime.sendMessage to send messages

  - Use browser.runtime.onMessage.addListener to listen for messages

  - Return true for asynchronous responses

  - Define clear message formats and types

  ### Handling Responses

  - Use Promises or async/await for asynchronous responses

  - Provide success/failure status

  - Include necessary data

  ## UI Design

  ### Component Structure

  - Keep interfaces clean and clear

  - Provide good loading states and error feedback

  - Use icons and colors appropriately to distinguish features

  ### User Experience

  - Provide visual feedback for all operations

  - Error messages should be friendly and offer solutions

  - Include operation confirmations and progress indicators

  ### Popup Window Design

  - Control reasonable window size (350-500px width)

  - Avoid excessive scrolling

  - Design clean headers and footer action areas

  ## Browser Compatibility

  ### Handling API Differences

  - Use browser abstractions provided by WXT

  - Provide different implementations for MV2 and MV3

  - Consider limitations of different browsers

  ### Special Page Handling

  - Detect and handle special pages (like new tabs, chrome:// settings)

  - Provide clear user prompts for special pages

  - Implement fallback options to enhance compatibility

  ## Performance Optimization

  ### Resource Usage

  - Avoid compute-intensive operations in background scripts

  - Use caching appropriately

  - Minimize cross-context communication

  ### Loading Strategies

  - Lazy load resources not immediately needed

  - Use async components

  - Consider code splitting

  ## Debugging and Testing

  ### Debugging Techniques

  - Use console.log to record information in different environments

  - Check extension background page and popup window consoles

  - Use browser developer tools' extension debugging features

  ### Testing Strategy

  - Test various page types (normal pages, special pages)

  - Test various error conditions and provide graceful fallbacks

  - Consider edge cases (such as permission denial)

  ## Building and Publishing

  ### Development and Building

  - Use npm run dev for development

  - Use npm run build to build production version

  - Use npm run zip to package the extension

  ### Version Control

  - Follow semantic versioning

  - Provide clear changelog

  - Follow Git branching strategy

  ### Publication Checklist

  - Ensure all permissions are explained

  - Provide clear descriptions and usage instructions

  - Include privacy policy

  - Test on all major browsers

  ## Specific Feature Implementation Guidelines

  ### Screenshot Functionality

  - Use the browser.tabs.captureVisibleTab API

  - Apply alternative screenshot strategies for special pages

  - Provide clear error messages

  - Include download functionality

  ### Page Interaction

  - Use content scripts to interact with pages

  - Consider using the scripting API to execute scripts

  - Handle cross-domain restrictions carefully

  ### Data Storage

  - Use browser.storage.local or browser.storage.sync

  - Consider data privacy and security

  - Implement data import/export functionality

  ### Notifications

  - Use the browser.notifications API

  - Limit notification frequency to avoid disturbing users

  - Provide options to close notifications

  ## Security Considerations

  ### Content Security

  - Avoid using innerHTML

  - Handle user input carefully

  - Consider using Content Security Policy (CSP)

  ### Data Privacy

  - Clearly state what data is collected

  - Provide data deletion options

  - Comply with privacy regulations

  ### Permission Usage

  - Only request permissions when needed

  - Provide explanations for permission usage

  - Consider scenarios where permissions are denied