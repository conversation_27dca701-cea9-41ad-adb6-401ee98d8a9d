<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9ea3195c-db1e-4b3c-b7ab-b99c20d25897" name="更改" comment="文档">
      <change beforePath="$PROJECT_DIR$/frontend/components/ScreenCapture.vue" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/components/ScreenCapture.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="30fra3IoBmNFemUT9xfzcdAhJ7o" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.imagetest.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/fire-wall-guard-v2/backend/agents&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\fire-wall-guard-v2\backend\agents" />
      <recent name="E:\fire-wall-guard-v2\backend" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="imagetest" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="fire-wall-guard-v2" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/backend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/backend/imagetest.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.imagetest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9ea3195c-db1e-4b3c-b7ab-b99c20d25897" name="更改" comment="" />
      <created>1754029528890</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754029528890</updated>
      <workItem from="1754029530303" duration="1170000" />
      <workItem from="1754030738786" duration="911000" />
      <workItem from="1754031668194" duration="3561000" />
      <workItem from="1754035573006" duration="5639000" />
      <workItem from="1754097208993" duration="9579000" />
      <workItem from="1754107975697" duration="1455000" />
      <workItem from="1754113464083" duration="851000" />
      <workItem from="1754114918439" duration="8027000" />
      <workItem from="1754129856037" duration="6716000" />
      <workItem from="1754183234306" duration="10391000" />
      <workItem from="1754199545678" duration="1048000" />
      <workItem from="1754204128199" duration="5066000" />
      <workItem from="1754279371973" duration="6726000" />
      <workItem from="1754355966357" duration="215000" />
      <workItem from="1754384178980" duration="1253000" />
    </task>
    <task id="LOCAL-00001" summary="修改第一天">
      <option name="closed" value="true" />
      <created>1754038941289</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754038941289</updated>
    </task>
    <task id="LOCAL-00002" summary="端口和ip配置">
      <option name="closed" value="true" />
      <created>1754040310797</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754040310797</updated>
    </task>
    <task id="LOCAL-00003" summary="时间和端口测试完成，优化代码结构之前">
      <option name="closed" value="true" />
      <created>1754122135427</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754122135427</updated>
    </task>
    <task id="LOCAL-00004" summary="代码重构完成">
      <option name="closed" value="true" />
      <created>1754123856230</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754123856230</updated>
    </task>
    <task id="LOCAL-00005" summary="form_router代码重构完成">
      <option name="closed" value="true" />
      <created>1754133243536</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754133243536</updated>
    </task>
    <task id="LOCAL-00006" summary="前后端测试完成">
      <option name="closed" value="true" />
      <created>1754209072328</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754209072328</updated>
    </task>
    <task id="LOCAL-00007" summary="前后端测试完成">
      <option name="closed" value="true" />
      <created>1754209238956</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754209238956</updated>
    </task>
    <task id="LOCAL-00008" summary="文档">
      <option name="closed" value="true" />
      <created>1754384203810</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1754384203810</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修改第一天" />
    <MESSAGE value="端口和ip配置" />
    <MESSAGE value="时间和端口测试完成，优化代码结构之前" />
    <MESSAGE value="代码重构完成" />
    <MESSAGE value="form_router代码重构完成" />
    <MESSAGE value="前后端测试完成" />
    <MESSAGE value="文档" />
    <option name="LAST_COMMIT_MESSAGE" value="文档" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/fire_wall_guard_v2$imagetest.coverage" NAME="imagetest 覆盖结果" MODIFIED="1754033030564" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
  </component>
</project>