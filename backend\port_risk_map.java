// Java Map格式
Map<String, String> portRiskMap = new HashMap<>();
portRiskMap.put("TCP:0", "禁止使用|“0”是无效端口");
portRiskMap.put("TCP:1", "禁止使用|存在默认无密码帐号");
portRiskMap.put("TCP:1001", "禁止使用|[木马] Silencer、WebEx 使用的通信端口");
portRiskMap.put("TCP:10067", "禁止使用|[木马] Portal of Doom 使用的通信端口");
portRiskMap.put("TCP:1011", "禁止使用|[木马] Doly Trojan 使用的通信端口");
portRiskMap.put("TCP:10167", "禁止使用|[木马] Portal of Doom 使用的通信端口");
portRiskMap.put("TCP:102", "禁止使用|Message transfer agent(MTA)-X.400 over
TCP/IP 通信端口");
portRiskMap.put("TCP:1024", "禁止使用|存在被监听攻击的风险");
portRiskMap.put("TCP:1025", "禁止使用|[木马] Fraggle Rock、[木马] md5 Backdoor、[木马] NetSpy、[木马] Remote Storm 默认使用的通信端口");
portRiskMap.put("TCP:1029", "禁止使用|[木马] Clandestine, [木马]  KWM, [木马]
Litmus, [木马] SubSARI 默认使用的通信端口");
portRiskMap.put("TCP:1033", "禁止使用|[木马]netspy 使用的通信端口");
portRiskMap.put("TCP:1080", "受控使用|存在穿过防火墙的风险");
portRiskMap.put("TCP:109", "受控使用|POP2、POP3 在提供邮件接收服务的同时，也出现了不少的漏洞。单单POP3 服务在用户名和密码交换缓冲区溢出的漏洞就不少于 20 个，比如 WebEasyMail POP3 Server 合法用户名信息泄露漏洞，通过该漏洞远程攻击者可以验证用户账户的存在。另外，110 端
口也被ProMail trojan 等木马程序所利用，通过 110 端口可以窃取POP 账号用户名和密
码。");
portRiskMap.put("TCP:110", "受控使用|POP2、POP3 在提供邮件接收服务的同时，也出现了不少的漏洞。单单POP3 服务在用户
名和密码交换缓冲区溢出的漏洞就不少于20 个，比如 WebEasyMail POP3 Server 合法用户名信息泄露漏洞，通过该漏洞远程攻击者可以验证用户账户的存在。另外，110 端口也被ProMail trojan 等木马程序所利用，通过 110 端口可以窃取POP 账号用户名和密
码。");
portRiskMap.put("TCP:11000", "禁止使用|[木马] SennaSpy 使用的通信端口");
portRiskMap.put("TCP:111", "受控使用|SUN RPC 有一个比较大漏洞，就是在多个 RPC服务时xdr_array 函数存在远程缓冲溢出漏洞，通过该漏洞允许攻击者传递超");
portRiskMap.put("TCP:11223", "禁止使用|[木马] Progenic trojan 使用的通信端口");
portRiskMap.put("TCP:113", "受控使用|113 端口虽然可以方便身份验证，但是也常常被作为FTP、POP、SMTP、IMAP 以及 IRC等网络服务的记录器，这样会被相应的木马程序所利用，比如基于 IRC 聊天室控制的木马。");
portRiskMap.put("TCP:115", "受控使用|存在弱密码和暴力破解风险，易造成敏感信息泄露。");
portRiskMap.put("TCP:1170", "禁止使用|[木马] Streaming Audio Trojan、Psyber Stream Server、Voice 使用的通信端口");
portRiskMap.put("TCP:119", "禁止使用|著名的Happy99 蠕虫病毒默认开放的就是 119 端口，如果中了该病毒会不断发送电子邮件进行传播，并造成网络的堵塞。");
portRiskMap.put("TCP:12076", "禁止使用|[木马] Telecommando 使用的通信端口");
portRiskMap.put("TCP:12223", "禁止使用|[木马] Hack'99 KeyLogger 使用的通信端口");
portRiskMap.put("TCP:1234", "禁止使用|[木马] SubSeven2.0、Ultors Trojan 使用
的通信端口");
portRiskMap.put("TCP:12345", "受控使用|[木马] NetBus1.60/1.70、GabanBus 使用的
通信端口");
portRiskMap.put("TCP:12346", "禁止使用|[木马] NetBus1.60/1.70、GabanBus 使用的通信端口");
portRiskMap.put("TCP:12361", "禁止使用|[木马] Whack-a-mole 使用的通信端口");
portRiskMap.put("TCP:1243", "禁止使用|[木马] SubSeven1.0/1.9 使用的通信端口");
portRiskMap.put("TCP:1245", "禁止使用|[木马] Vodoo 使用的通信端口");
portRiskMap.put("TCP:13223", "禁止使用|这一程序对于建立连接非常具有“进攻性”。它会“驻扎”在这一TCP 端口等待回应。这造成类似心跳间隔的连接企图。如果你是一个拨号用户，从另一个聊天者手中“继承”了IP 地址这种情况就会发生：好象很多不
同的人在测试这一端口。");
portRiskMap.put("TCP:135", "受控使用|很多Windows 2000 和 Windows XP 用户都中了“冲击波”病毒，该病毒就是利用 RPC 漏洞来攻击计算机的。RPC 本身在处理通过 TCP/IP 的消息交换部分有一个漏洞，该漏洞是由于错误地处理格式不正确的消息造成的。该漏洞会影响到RPC 与DCOM 之间的一
个接口，该接口侦听的端口就是 135。");
portRiskMap.put("TCP:1352", "受控使用|控制台弱口令，信息泄露，xss 漏洞");
portRiskMap.put("TCP:137", "受控使用|因为是UDP 端口，对于攻击者来说，通过发送请求很容易就获取目标计算机的相关信息，有些信息是直接可以被利用，并分析漏洞的，比如 IIS 服务。另外，通过捕获正在");
portRiskMap.put("TCP:138", "受控使用|因为是UDP 端口，对于攻击者来说，通过发送请求很容易就获取目标计算机的相关信息，有些信息是直接可以被利用，并分析漏洞的，比如 IIS 服务。另外，通过捕获正在利用 138 端口进行通信的信息包，还可能得到目标计算机的启动和关闭的时间，这样就可以利用专门的工具来攻击。");
portRiskMap.put("TCP:139", "受控使用|可以提供共享服务，但是常常被攻击者所利用进行攻击，比如使用流光、SuperScan 等端口扫描工具，可以扫描目标计算机的 139端口，如果发现有漏洞，可以试图获取用户
名和密码，这是非常危险的。");
portRiskMap.put("TCP:143", "受控使用|同POP3 协议的 110 端口一样，IMAP 使用的 143 端口也存在缓冲区溢出漏洞，通过该漏洞可以获取用户名和密码。另外，还有一种名为“admv0rm”的 Linux 蠕虫病毒会利用该端口进行繁殖。");
portRiskMap.put("TCP:1433", "受控使用|黑客经常通过该端口对SQL server 服务进行暴力破解，达到控制服务器的目的，SQL
Server 本地提权漏洞，可用于权限提升。");
portRiskMap.put("TCP:1434", "受控使用|黑客经常通过该端口对SQL server 服务进行暴力破解，达到控制服务器的目的，SQL Server 本地提权漏洞，可用于权限提升。");
portRiskMap.put("TCP:1492", "禁止使用|[木马] FTP99CMP 使用的通信端口");
portRiskMap.put("TCP:1494", "受控使用|citrix ICA 服务");
portRiskMap.put("TCP:1500", "受控使用|RPC 客户会话查询固定端口");
portRiskMap.put("TCP:1503", "受控使用|存在缓冲区溢出漏洞");
portRiskMap.put("TCP:1521", "受控使用|黑客经常通过该端口对Oracle 服务进行暴
力破解，达到控制服务器的目的");
portRiskMap.put("TCP:1524", "禁止使用|存在后门shell");
portRiskMap.put("TCP:1600", "禁止使用|[木马] Shivka-Burka 使用的通信端口");
portRiskMap.put("TCP:16969", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:17027", "禁止使用|存在黑客利用共享软件外向连接的风险");
portRiskMap.put("TCP:1720", "受控使用|NetMeeting 使用通信端口");
portRiskMap.put("TCP:1731", "受控使用|NetMeeting 使用通信端口");
portRiskMap.put("TCP:177", "受控使用|LDAP、ILS
开放此端口。");
portRiskMap.put("TCP:1807", "禁止使用|[木马] SpySender 使用的通信端口");
portRiskMap.put("TCP:19", "禁止使用|存在 dos 攻击风险");
portRiskMap.put("TCP:19191", "禁止使用|[木马] 蓝色火焰使用的通信端口");
portRiskMap.put("TCP:1981", "禁止使用|[木马] ShockRave 使用的通信端口");
portRiskMap.put("TCP:1999", "禁止使用|[木马] BackDoor 使用的通信端口");
portRiskMap.put("TCP:2000", "禁止使用|[木马] 木马 GirlFriend 1.3、Millenium
1.0 使用的通信端口");
portRiskMap.put("TCP:20000", "受控使用|[木马] Millennium 使用的通信端口");
portRiskMap.put("TCP:20001", "受控使用|[木马] Millennium 使用的通信端口");
portRiskMap.put("TCP:2001", "禁止使用|[木马] Millenium 1.0、Trojan Cow 使用的
通信端口");
portRiskMap.put("TCP:20034", "禁止使用|[木马] NetBus Pro 使用的通信端口");
portRiskMap.put("TCP:2023", "禁止使用|[木马] Pass Ripper 使用的通信端口");
portRiskMap.put("TCP:20432", "禁止使用|[木马] Shaft 的通信端口，该木马是一个用
于进行DDOS 攻击的木马");
portRiskMap.put("TCP:2049", "受控使用|NFS 使用通信端口");
portRiskMap.put("TCP:21", "受控使用|FTP 的传输通道不加密，容易被在传输过程中被窃听，造成信息泄露，FTP 允许匿名登录，FTP 使用弱口令或默认口令，FTP 存在
远程代码执行漏洞，FTP 未做登录次数限制。");
portRiskMap.put("TCP:2115", "禁止使用|[木马] Bugs 使用的通信端口");
portRiskMap.put("TCP:2140", "禁止使用|[木马] Deep Throat 1.0/3.0 使用的通信端
口");
portRiskMap.put("TCP:21554", "禁止使用|[木马] Exploiter，[木马] Kid Terror，[木马] Winsp00fer，[木马] GirlFriend，[木马] Schwindler 默认使用的通信端口");
portRiskMap.put("TCP:22", "受控使用|存在暴力破解风险，存在心脏滴血漏洞。");
portRiskMap.put("TCP:22222", "禁止使用|[木马] Prosiak 使用的通信端口");
portRiskMap.put("TCP:23", "受控使用|telnet 的传输通道不加密，可以被网络监 听，并获取信息，造成敏感信息泄露。另外黑客可以通过对telnet 服务进行暴力破解，达到控制服务器的目的。[漏洞]
CVE-2012-2015，[漏洞] CVE-2012-1222，[漏洞] CVE-2012-4703，[漏洞]CVE-2012-5345， [漏洞]CVE-2015-3459，[漏
洞]CVE-2015-8286。");
portRiskMap.put("TCP:23456", "禁止使用|[木马] Evil FTP、Ugly FTP 使用的通信端
口");
portRiskMap.put("TCP:2375", "受控使用|docker 远程管理端口，可以控制服务器");
portRiskMap.put("TCP:2425", "受控使用|部分老伴飞秋程序存在溢出漏洞，利用该漏
洞可以实现控制目标主机。");
portRiskMap.put("TCP:2500", "受控使用|固定端口会话复制的RPC 客户端口");
portRiskMap.put("TCP:25565", "禁止使用|存在黑客利用游戏默认端口联机控制的风险");
portRiskMap.put("TCP:2583", "禁止使用|[木马] Wincrash 2.0 使用的通信端口");
portRiskMap.put("TCP:26274", "禁止使用|[木马] Delta 使用的通信端口");
portRiskMap.put("TCP:2638", "受控使用|该端口的开放使得攻击者可以猜解密码、远
程执行代码以及DDOS 攻击影响业务使用。");
portRiskMap.put("TCP:27017", "受控使用|MongoDb 弱口令，未授权访问");
portRiskMap.put("TCP:27374", "禁止使用|[木马] Subseven 2.1 使用的通信端口");
portRiskMap.put("TCP:2745", "禁止使用|[木马]Beagle，使用自己的SMTP 引擎，并打开端口 2745。一个后门，他们通过电子邮件和文件共享网络传播群发邮件蠕虫");
portRiskMap.put("TCP:2801", "禁止使用|[木马] Phineas Phucker 使用的通信端口");
portRiskMap.put("TCP:30003", "受控使用|[漏洞] CVE-2012-0698 。[木马] Lamers
Death trojan 使用的通信端口");
portRiskMap.put("TCP:30100", "禁止使用|[木马] NetSphere 使用的通信端口");
portRiskMap.put("TCP:3024", "禁止使用|[木马] WinCrash 使用的通信端口");
portRiskMap.put("TCP:30303", "禁止使用|[木马] Socket23 使用的通信端口");
portRiskMap.put("TCP:30999", "禁止使用|[木马] Kuang 使用的通信端口");
portRiskMap.put("TCP:31", "禁止使用|[木马] Master Paradise、Hackers Paradise使用的通信端口");
portRiskMap.put("TCP:3127", "禁止使用|[病毒]W32/MyDoom、[蠕虫]A.K.A W32/Mydoom@MM，当一台计算机被感染，该蠕虫会通过 3198 打开 TCP 端口 3127， compromissing 整个系统设置了一个后门进
入系统");
portRiskMap.put("TCP:3128", "受控使用|squid HTTP 代理服务器的默认端口，攻击代理服务器");
portRiskMap.put("TCP:3129", "禁止使用|[木马] Master Paradise 使用的通信端口");
portRiskMap.put("TCP:31337", "禁止使用|[木马] BO(Back Orifice)使用的通信端口");
portRiskMap.put("TCP:31338", "禁止使用|[木马] BO(Back Orifice)使用的通信端口");
portRiskMap.put("TCP:31339", "禁止使用|[木马] LittleWitch, [木马] Net Spy 使用的通信端口");
portRiskMap.put("TCP:3150", "禁止使用|[木马] Deep Throat 1.0/3.0 使用的通信端口，[木马] The Invasor 使用的通信端口");
portRiskMap.put("TCP:31666", "禁止使用|[木马] BOWhack 使用的通信端口");
portRiskMap.put("TCP:31789", "禁止使用|木马Hack a tack 开放此端口。");
portRiskMap.put("TCP:3210", "禁止使用|[木马] SchoolBus 使用的通信端口");
portRiskMap.put("TCP:3306", "受控使用|黑客经常通过该端口对mysql 服务进行暴力破解，达到控制服务器的目的，Mysql 本地
提前漏洞，可用于权限提升");
portRiskMap.put("TCP:3333", "受控使用|[蠕虫]W32.Bratle.A，蠕虫病毒利用微软 Windows LSASS 缓冲区溢出漏洞（MS04-011）进行传播；[木马]Daodan，[木
马]Backdoor.Slao");
portRiskMap.put("TCP:33333", "禁止使用|[木马] Prosiak 使用的通信端口");
portRiskMap.put("TCP:3389", "受控使用|黑客经常通过暴力猜解对用户名口令进行猜解，达到控制服务器的目的");
portRiskMap.put("TCP:34324", "禁止使用|[木马] Tiny Telnet Server、BigGluck、 TN 使用的通信端口");
portRiskMap.put("TCP:3700", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:389", "受控使用|存在弱口令，允许匿名登录造成的未授权访问，SQL 注入");
portRiskMap.put("TCP:3996", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:40412", "禁止使用|[木马] The Spy 使用的通信端口");
portRiskMap.put("TCP:40421", "禁止使用|[木马] Masters Paradise 使用的通信端口");
portRiskMap.put("TCP:40422", "禁止使用|[木马] Masters Paradise 使用的通信端口");
portRiskMap.put("TCP:40423", "禁止使用|[木马] Masters Paradise 使用的通信端口");
portRiskMap.put("TCP:40426", "禁止使用|[木马] Masters Paradise 使用的通信端口");
portRiskMap.put("TCP:4060", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:4092", "禁止使用|[木马] WinCrash 使用的通信端口");
portRiskMap.put("TCP:4100", "受控使用|该端口的开放使得攻击者可以猜解密码、远
程执行代码以及DDOS 攻击影响业务使用。");
portRiskMap.put("TCP:4321", "禁止使用|[木马] SchoolBus 使用的通信端口");
portRiskMap.put("TCP:43210", "禁止使用|[木马] SchoolBus 1.0/2.0 使用的通信端口");
portRiskMap.put("TCP:4422", "禁止使用|[木马]Backdoor.Acidoor，默认情况下，
Extapp.exe 文件使用4432 或4433 端口是感染的该木马的一个表现。");
portRiskMap.put("TCP:4433", "禁止使用|[木马]Backdoor.Acidoor，默认情况下， Extapp.exe 文件使用4432 或4433 端口是感染的该木马的一个表现。");
portRiskMap.put("TCP:4444", "禁止使用|[木马]CrackDown, [木马]Prosiak，[木
马]Swift Remote，[蠕虫]W32.Blaster.Worm默认使用的通信端口");
portRiskMap.put("TCP:44445", "禁止使用|[木马] Happypig 使用的通信端口");
portRiskMap.put("TCP:445", "受控使用|445 端口是 IPC$入侵的主要通道，黑客可以利用该端口漏洞访问默认共享文件，甚至格式化硬盘。");
portRiskMap.put("TCP:456", "禁止使用|木马HACKERS PARADISE 开放此端口。");
portRiskMap.put("TCP:4590", "禁止使用|[木马] ICQTrojan 使用的通信端口");
portRiskMap.put("TCP:47262", "禁止使用|[木马] Delta 使用的通信端口");
portRiskMap.put("TCP:47878", "禁止使用|木马BirdSpy2 开放此端口。");
portRiskMap.put("TCP:4899", "禁止使用|[远控] Radmin，该端口是远程控制软件
Radmin 的默认服务端口，通过该端口可以与");
portRiskMap.put("TCP:5000", "受控使用|该端口的开放使得攻击者可以猜解密码、远程执行代码以及DDOS 攻击影响业务使用；数据库弱口令，命令注入漏洞；
[木马] blazer5 使用的通信端口。");
portRiskMap.put("TCP:5001", "禁止使用|[木马] Sockets de Troie 使用的通信端口");
portRiskMap.put("TCP:5002", "禁止使用|[漏洞] CVE-2002-0496，[漏洞]
CVE-2011-0272，[木马] Shaft，[木
马]cd00r，[木马]Linux Rootkit IV (4)默认使用的通信端口");
portRiskMap.put("TCP:50505", "禁止使用|[木马] Sockets de Troie 使用的通信端口");
portRiskMap.put("TCP:50766", "禁止使用|[木马] Fore 使用的通信端口");
portRiskMap.put("TCP:513", "禁止使用|Login,remote login 通信端口");
portRiskMap.put("TCP:514", "禁止使用|远程shell(rshell)和远程复制为入侵者进
入他们的系统提供了信息。");
portRiskMap.put("TCP:53001", "禁止使用|[木马] Remote Windows Shutdown 使用的通信端口");
portRiskMap.put("TCP:5321", "禁止使用|[木马] Sockets de Troie 使用的通信端口");
portRiskMap.put("TCP:5400", "禁止使用|[木马] Back Construction，[木马] Blade Runner，[木马] Digital Spy 使用的通信端口");
portRiskMap.put("TCP:5401", "禁止使用|[木马] Blade Runner 使用的通信端口");
portRiskMap.put("TCP:5402", "禁止使用|[木马] Blade Runner 使用的通信端口");
portRiskMap.put("TCP:54321", "禁止使用|[漏洞] CVE-2010-4741，[漏洞]
CVE-2014-0327。[木马]School Bus , [木马]yoyo 使用的通信端口");
portRiskMap.put("TCP:544", "受控使用|kerberos kshell 通信端口");
portRiskMap.put("TCP:548", "禁止使用|Macintosh 通信端口");
portRiskMap.put("TCP:554", "受控使用|RTSP 协议所发现的漏洞主要就是 RealNetworks 早期发布的 Helix Universal Server 存在缓冲区溢出漏洞");
portRiskMap.put("TCP:555", "禁止使用|[木马] PhAse1.0、Stealth Spy、IniKiller使用的通信端口");
portRiskMap.put("TCP:5550", "禁止使用|[木马] xtcp 使用的通信端口");
portRiskMap.put("TCP:5554", "禁止使用|[木马] Sasser Worm，[漏洞]MS04-011，[病
毒]W32.Dabber 使用的通信端口");
portRiskMap.put("TCP:5569", "禁止使用|[木马] Robo-Hack 使用的通信端口");
portRiskMap.put("TCP:5631", "受控使用|pcanywhere 软件远程管理通信端口，该端口
的开放使得攻击者可以猜解密码。");
portRiskMap.put("TCP:5632", "受控使用|存在扫描局域网风险");
portRiskMap.put("TCP:568", "禁止使用|Membership DPA 通信端口");
portRiskMap.put("TCP:569", "禁止使用|Membership DPA 通信端口");
portRiskMap.put("TCP:5742", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:5900", "受控使用|[远控]VNC，该端口是远程控制软件 VNC 的默认服务端口，通过该端口可以与服务器进行通信并进行远程控制。");
portRiskMap.put("TCP:5901", "受控使用|[远控]VNC，该端口是远程控制软件 VNC 的默认服务端口，通过该端口可以与服务器进
行通信并进行远程控制。");
portRiskMap.put("TCP:593", "禁止使用|RPC 缓冲区溢出漏洞 MS03-026，可以通过端口 135，139，445，593（或任何其他特殊配
置的RPC 端口）被利用");
portRiskMap.put("TCP:6000", "受控使用|X-windows 软件远程管理通信端口，该端口的开放使得攻击者可以猜解密码。");
portRiskMap.put("TCP:6112", "禁止使用|[漏洞] CVE-2001-0803，一个存在通用桌面环境（CDE）子进程控制服务（dtspcd）的远程利用缓冲区溢出。攻击者成功利用此漏
洞可以执行任意代码。");
portRiskMap.put("TCP:6129", "禁止使用|[远控] DameWare，该端口是远程控制软件 DameWare 的默认服务端口，通过该端口可以
与服务器进行通信并进行远程控制，");
portRiskMap.put("TCP:61466", "禁止使用|[木马] Telecommando 使用的通信端口");
portRiskMap.put("TCP:6267", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:635", "禁止使用|存在扫描漏洞");
portRiskMap.put("TCP:636", "受控使用|存在ssl 漏洞");
portRiskMap.put("TCP:6379", "受控使用|弱口令，未授权访问，配合ssh key 提权；漏洞较多。");
portRiskMap.put("TCP:6400", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:64101", "禁止使用|[木马] Taskman trojan 使用的通信端口");
portRiskMap.put("TCP:65000", "禁止使用|[木马] Devil 1.03 使用的通信端口");
portRiskMap.put("TCP:666", "禁止使用|[木马] Attack FTP、Satanz Backdoor 使用
的通信端口");
portRiskMap.put("TCP:6670", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:6671", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:6711", "禁止使用|[木马] SubSeven1.0/1.9 使用的通信端口");
portRiskMap.put("TCP:6712", "禁止使用|[木马] BackDoor-G, [木马]SubSeven (Sub7)，[木马]KiLo trojan，[木马]Funny trojan 使用的通信端口");
portRiskMap.put("TCP:6776", "禁止使用|[木马] SubSeven2.0、SubSeven1.0/1.9 使用的通信端口");
portRiskMap.put("TCP:6883", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:6969", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:7", "禁止使用|echo 通信端口");
portRiskMap.put("TCP:70", "禁止使用|可以模拟任何服务访问方式，容易造成 SSRF攻击");
portRiskMap.put("TCP:7000", "受控使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:7001", "受控使用|对使用默认WEB 管理端口的weblogic 服务器存在暴露后台的风险，Weblogic 反序列漏洞，Weblogic uuid csrf 及 xss 漏洞");
portRiskMap.put("TCP:7300", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:7301", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:7306", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:7307", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("TCP:7308", "禁止使用|[木马] NetMonitor 使用的通信端口");
portRiskMap.put("TCP:7323", "禁止使用|[木马] Sygate 使用的通信端口");
portRiskMap.put("TCP:7626", "禁止使用|[木马] Giscier 使用的通信端口");
portRiskMap.put("TCP:777", "禁止使用|[漏洞]CVE-2011-0406，[木马] AimSpy，[木马] a.k.a. Backdoor.TDS, [木马]4Fuk,
[木马]Trojan.Win32.TrojanRunner.Levil
使用的通信端口，");
portRiskMap.put("TCP:7789", "禁止使用|[木马] ICKiller 使用的通信端口");
portRiskMap.put("TCP:79", "禁止使用|一般黑客要攻击对方的计算机，都是通过相应的端口扫描工具来获得相关信息，比如使用“流光”就可以利用 79 端口来扫描远程计算机操作系统版本，获得用户信息，还能探测已知的缓冲区溢出错误。这样，就容易遭遇到黑客的攻击。而且，79 端口还被
Firehotcker 木马作为默认的端口。");
portRiskMap.put("TCP:8010", "受控使用|可以被病毒程序所利用，遥控被感染的计算
机");
portRiskMap.put("TCP:8080", "受控使用|Apache Tomcat 和 Apache JBoss 的默认 WEB管理端口，Jboss 反序列化漏洞。端口风险：对使用默认WEB 管理端口的Apache Tomcat");
portRiskMap.put("TCP:8089", "受控使用|Jenkens 反序列化漏洞，Jenkens 控制台弱口令或默认口令");
portRiskMap.put("TCP:8161", "受控使用|消息队列服务，默认admin：admin");
portRiskMap.put("TCP:873", "受控使用|可以往服务器上同步上传下载文件");
portRiskMap.put("TCP:88", "受控使用");
portRiskMap.put("TCP:9080", "受控使用|控制台弱口令爆破，Websphere 反序列化漏
洞，CVE-2014-0823（任意文件泄露漏洞）");
portRiskMap.put("TCP:9081", "受控使用|控制台弱口令爆破，Websphere 反序列化漏洞，CVE-2014-0823（任意文件泄露漏洞）");
portRiskMap.put("TCP:9090", "受控使用|控制台弱口令爆破，Websphere 反序列化漏
洞，CVE-2014-0823（任意文件泄露漏洞）");
portRiskMap.put("TCP:9200", "受控使用|检索服务，1.2 版本之前有命令执行漏洞");
portRiskMap.put("TCP:9400", "禁止使用|[木马] Incommand 1.0 使用的通信端口");
portRiskMap.put("TCP:9401", "禁止使用|[木马] Incommand 1.0 使用的通信端口");
portRiskMap.put("TCP:9402", "禁止使用|[木马] Incommand 1.0 使用的通信端口");
portRiskMap.put("TCP:9875", "禁止使用|[木马] Portal of Doom 使用的通信端口");
portRiskMap.put("TCP:99", "禁止使用|虽然“Metagram Relay”服务不常用，可是 Hidden Port、NCx99 等木马程序会利用该端口，比如在 Windows 2000 中，NCx99 可以把 cmd.exe 程序绑定到 99 端口，这样用 Telnet就可以连接到服务器，随意添加用户、更改权限。");
portRiskMap.put("TCP:993", "受控使用|存在ssl 漏洞");
portRiskMap.put("TCP:9989", "禁止使用|[木马] iNi-Killer 使用的通信端口");
portRiskMap.put("UDP:0", "禁止使用|“0”是无效端口");
portRiskMap.put("UDP:10067", "禁止使用|[木马] Portal of Doom 使用的通信端口");
portRiskMap.put("UDP:10167", "禁止使用|[木马] Portal of Doom 使用的通信端口");
portRiskMap.put("UDP:1025", "禁止使用|[木马] Fraggle Rock、[木马] md5 Backdoor、[木马] NetSpy、[木马] Remote Storm 默认使用的通信端口");
portRiskMap.put("UDP:1029", "禁止使用|[木马] Clandestine, [木马]  KWM, [木马]
Litmus, [木马] SubSARI 默认使用的通信端口");
portRiskMap.put("UDP:111", "受控使用|SUN RPC 有一个比较大漏洞，就是在多个 RPC服务时xdr_array 函数存在远程缓冲溢出漏洞，通过该漏洞允许攻击者传递超");
portRiskMap.put("UDP:12345", "受控使用|[木马] NetBus1.60/1.70、GabanBus 使用的
通信端口");
portRiskMap.put("UDP:135", "受控使用|很多Windows 2000 和 Windows XP 用户都中了“冲击波”病毒，该病毒就是利用 RPC 漏洞来攻击计算机的。RPC 本身在处理通过 TCP/IP 的消息交换部分有一个漏洞，该漏洞是由于错误地处理格式不正确的消息造成的。该漏洞会影响到RPC 与DCOM 之间的一
个接口，该接口侦听的端口就是 135。");
portRiskMap.put("UDP:1352", "受控使用|控制台弱口令，信息泄露，xss 漏洞");
portRiskMap.put("UDP:137", "受控使用|因为是UDP 端口，对于攻击者来说，通过发送请求很容易就获取目标计算机的相关信息，有些信息是直接可以被利用，并分析漏洞的，比如 IIS 服务。另外，通过捕获正在");
portRiskMap.put("UDP:138", "受控使用|因为是UDP 端口，对于攻击者来说，通过发送请求很容易就获取目标计算机的相关信息，有些信息是直接可以被利用，并分析漏洞的，比如 IIS 服务。另外，通过捕获正在利用 138 端口进行通信的信息包，还可能得到目标计算机的启动和关闭的时间，这样就可以利用专门的工具来攻击。");
portRiskMap.put("UDP:139", "受控使用|可以提供共享服务，但是常常被攻击者所利用进行攻击，比如使用流光、SuperScan 等端口扫描工具，可以扫描目标计算机的 139端口，如果发现有漏洞，可以试图获取用户
名和密码，这是非常危险的。");
portRiskMap.put("UDP:1433", "受控使用|黑客经常通过该端口对SQL server 服务进行暴力破解，达到控制服务器的目的，SQL
Server 本地提权漏洞，可用于权限提升。");
portRiskMap.put("UDP:1434", "受控使用|黑客经常通过该端口对SQL server 服务进行暴力破解，达到控制服务器的目的，SQL Server 本地提权漏洞，可用于权限提升。");
portRiskMap.put("UDP:161", "受控使用|因为通过SNMP 可以获得网络中各种设备的状态信息，还能用于对网络设备的控制，所以黑客可以通过SNMP 漏洞来完全控制网络。存在默认的秘密public、private 访问系统.");
portRiskMap.put("UDP:1701", "受控使用|建立VPN 服务");
portRiskMap.put("UDP:1812", "受控使用|radius 认证服务");
portRiskMap.put("UDP:1813", "受控使用|radius 记账服务");
portRiskMap.put("UDP:19", "禁止使用|存在 dos 攻击风险");
portRiskMap.put("UDP:19132", "禁止使用|存在黑客利用游戏默认端口联机控制的风险");
portRiskMap.put("UDP:2000", "禁止使用|[木马] 木马 GirlFriend 1.3、Millenium
1.0 使用的通信端口");
portRiskMap.put("UDP:2001", "禁止使用|[木马] Millenium 1.0、Trojan Cow 使用的
通信端口");
portRiskMap.put("UDP:2049", "受控使用|NFS 使用通信端口");
portRiskMap.put("UDP:2115", "禁止使用|[木马] Bugs 使用的通信端口");
portRiskMap.put("UDP:2140", "禁止使用|[木马] Deep Throat 1.0/3.0 使用的通信端
口");
portRiskMap.put("UDP:21554", "禁止使用|[木马] Exploiter，[木马] Kid Terror，[木马] Winsp00fer，[木马] GirlFriend，[木马] Schwindler 默认使用的通信端口");
portRiskMap.put("UDP:26274", "禁止使用|[木马] Delta 使用的通信端口");
portRiskMap.put("UDP:31337", "禁止使用|[木马] BO(Back Orifice)使用的通信端口");
portRiskMap.put("UDP:31338", "禁止使用|[木马] BO(Back Orifice)使用的通信端口");
portRiskMap.put("UDP:31339", "禁止使用|[木马] LittleWitch, [木马] Net Spy 使用的通信端口");
portRiskMap.put("UDP:3150", "禁止使用|[木马] Deep Throat 1.0/3.0 使用的通信端口，[木马] The Invasor 使用的通信端口");
portRiskMap.put("UDP:31789", "禁止使用|木马Hack a tack 开放此端口。");
portRiskMap.put("UDP:389", "受控使用|存在弱口令，允许匿名登录造成的未授权访问，SQL 注入");
portRiskMap.put("UDP:3996", "禁止使用|[木马] Priority 使用的通信端口");
portRiskMap.put("UDP:4011", "禁止使用|服务器通过网络启动的服务，建议在单独网络使用。");
portRiskMap.put("UDP:4444", "禁止使用|[木马]CrackDown, [木马]Prosiak，[木
马]Swift Remote，[蠕虫]W32.Blaster.Worm默认使用的通信端口");
portRiskMap.put("UDP:47262", "禁止使用|[木马] Delta 使用的通信端口");
portRiskMap.put("UDP:5000", "受控使用|该端口的开放使得攻击者可以猜解密码、远程执行代码以及DDOS 攻击影响业务使用；数据库弱口令，命令注入漏洞；
[木马] blazer5 使用的通信端口。");
portRiskMap.put("UDP:5001", "禁止使用|[木马] Sockets de Troie 使用的通信端口");
portRiskMap.put("UDP:5002", "禁止使用|[漏洞] CVE-2002-0496，[漏洞]
CVE-2011-0272，[木马] Shaft，[木
马]cd00r，[木马]Linux Rootkit IV (4)默认使用的通信端口");
portRiskMap.put("UDP:514", "禁止使用|远程shell(rshell)和远程复制为入侵者进
入他们的系统提供了信息。");
portRiskMap.put("UDP:54321", "禁止使用|[漏洞] CVE-2010-4741，[漏洞]
CVE-2014-0327。[木马]School Bus , [木马]yoyo 使用的通信端口");
portRiskMap.put("UDP:553", "禁止使用|CORBA IIOP （UDP）通信端口");
portRiskMap.put("UDP:554", "受控使用|RTSP 协议所发现的漏洞主要就是 RealNetworks 早期发布的 Helix Universal Server 存在缓冲区溢出漏洞");
portRiskMap.put("UDP:6112", "禁止使用|[漏洞] CVE-2001-0803，一个存在通用桌面环境（CDE）子进程控制服务（dtspcd）的远程利用缓冲区溢出。攻击者成功利用此漏
洞可以执行任意代码。");
portRiskMap.put("UDP:635", "禁止使用|存在扫描漏洞");
portRiskMap.put("UDP:666", "禁止使用|[木马] Attack FTP、Satanz Backdoor 使用
的通信端口");
portRiskMap.put("UDP:67", "受控使用|如果开放Bootp 服务，常常会被黑客利用分配的一个IP 地址作为局部路由器通过“中间人”(man-in-middle)方式进行攻击。");
portRiskMap.put("UDP:68", "受控使用|如果开放Bootp 服务，常常会被黑客利用分配的一个IP 地址作为局部路由器通过“中间人”(man-in-middle)方式进行攻击。");
portRiskMap.put("UDP:69", "受控使用|很多服务器和Bootp 服务一起提供TFTP 服务，主要用于从系统下载启动代码。可是，因为TFTP 服务可以在系统中写入文件，而且黑客还可以利用TFTP 的错误配置来从系统获取任何文件。");
portRiskMap.put("UDP:6970", "受控使用|RealAudio 客户端口通信端口");
portRiskMap.put("UDP:7", "禁止使用|echo 通信端口");
portRiskMap.put("UDP:873", "受控使用|可以往服务器上同步上传下载文件");
portRiskMap.put("UDP:88", "受控使用");
portRiskMap.put("UDP:99", "禁止使用|虽然“Metagram Relay”服务不常用，可是 Hidden Port、NCx99 等木马程序会利用该端口，比如在 Windows 2000 中，NCx99 可以把 cmd.exe 程序绑定到 99 端口，这样用 Telnet就可以连接到服务器，随意添加用户、更改权限。");