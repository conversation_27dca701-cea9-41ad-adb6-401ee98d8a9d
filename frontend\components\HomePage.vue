<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { getApiConfig, saveApiConfig, checkApiHealth, DEFAULT_API_CONFIG, type ApiConfig } from './config';

// 定义事件
const emit = defineEmits(['navigate']);

// 状态变量
const showSettings = ref(false);
const apiConfig = ref<ApiConfig>({ ...DEFAULT_API_CONFIG });
const tempConfig = ref<ApiConfig>({ ...DEFAULT_API_CONFIG });
const healthStatus = ref<{ success: boolean; message: string; checking: boolean }>({
  success: false,
  message: '未检测',
  checking: false
});
const saveLoading = ref(false);

// 导航到指定页面
function navigateTo(page: string) {
  emit('navigate', page);
}

// 显示设置面板
function showSettingsPanel() {
  showSettings.value = true;
  tempConfig.value = { ...apiConfig.value };
}

// 隐藏设置面板
function hideSettingsPanel() {
  showSettings.value = false;
  tempConfig.value = { ...apiConfig.value };
}

// 检测API连接
async function testConnection() {
  healthStatus.value.checking = true;

  try {
    // 直接使用临时配置进行测试，不修改存储的配置
    const result = await checkApiHealth(tempConfig.value);
    healthStatus.value = {
      success: result.success,
      message: result.message,
      checking: false
    };
  } catch (error) {
    healthStatus.value = {
      success: false,
      message: '检测失败',
      checking: false
    };
  }
}

// 保存配置
async function saveSettings() {
  try {
    saveLoading.value = true;

    // 验证输入
    if (!tempConfig.value.host.trim()) {
      throw new Error('请输入有效的主机地址');
    }

    if (!tempConfig.value.port || tempConfig.value.port < 1 || tempConfig.value.port > 65535) {
      throw new Error('请输入有效的端口号 (1-65535)');
    }

    // 保存配置
    await saveApiConfig(tempConfig.value);
    apiConfig.value = { ...tempConfig.value };

    // 重新检测连接
    await testConnection();

    showSettings.value = false;
  } catch (error) {
    console.error('保存配置失败:', error);
    alert(error instanceof Error ? error.message : '保存配置失败');
  } finally {
    saveLoading.value = false;
  }
}

// 重置为默认配置
function resetToDefault() {
  tempConfig.value = { ...DEFAULT_API_CONFIG };
}

// 组件挂载时加载配置
onMounted(async () => {
  try {
    const config = await getApiConfig();
    apiConfig.value = config;
    tempConfig.value = { ...config };

    // 自动检测连接状态
    const result = await checkApiHealth();
    healthStatus.value = {
      success: result.success,
      message: result.message,
      checking: false
    };
  } catch (error) {
    console.error('加载配置失败:', error);
  }
});
</script>

<template>
  <div class="home-page">
    <!-- API连接状态显示 -->
    <div class="api-status" :class="{ 'status-success': healthStatus.success, 'status-error': !healthStatus.success && healthStatus.message !== '未检测' }">
      <div class="status-info">
        <span class="status-icon">{{ healthStatus.success ? '🟢' : '🔴' }}</span>
        <span class="status-text">{{ healthStatus.message }}</span>
        <span v-if="healthStatus.checking" class="checking">检测中...</span>
      </div>
      <button @click="showSettingsPanel" class="settings-btn" title="API设置">
        ⚙️
      </button>
    </div>

    <p class="description">请选择一种信息获取方式：</p>

    <div class="method-buttons">
      <button
        @click="navigateTo('screenshot')"
        class="method-btn screenshot-btn"
        :disabled="!healthStatus.success"
      >
        <div class="btn-icon">📷</div>
        <div class="btn-content">
          <div class="btn-title">页面截图方式</div>
          <div class="btn-desc">通过截取当前页面并发送到后端进行解析</div>
        </div>
      </button>

      <button
        @click="navigateTo('dom')"
        class="method-btn dom-btn"
        :disabled="!healthStatus.success"
      >
        <div class="btn-icon">🔍</div>
        <div class="btn-content">
          <div class="btn-title">DOM解析方式</div>
          <div class="btn-desc">智能识别防火墙策略表格或通用表单数据</div>
        </div>
      </button>
    </div>

    <!-- 设置面板遮罩 -->
    <div v-if="showSettings" class="settings-overlay" @click="hideSettingsPanel">
      <div class="settings-panel" @click.stop>
        <div class="settings-header">
          <h3>API服务器设置</h3>
          <button @click="hideSettingsPanel" class="close-btn">&times;</button>
        </div>

        <div class="settings-content">
          <div class="form-group">
            <label for="host">主机地址:</label>
            <input
              id="host"
              v-model="tempConfig.host"
              type="text"
              placeholder="127.0.0.1"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="port">端口号:</label>
            <input
              id="port"
              v-model.number="tempConfig.port"
              type="number"
              min="1"
              max="65535"
              placeholder="8000"
              class="form-input"
            />
          </div>

          <div class="current-url">
            <strong>当前API地址:</strong>
            <code>http://{{ tempConfig.host }}:{{ tempConfig.port }}</code>
          </div>

          <div class="test-connection">
            <button
              @click="testConnection"
              :disabled="healthStatus.checking"
              class="test-btn"
            >
              <span v-if="healthStatus.checking" class="loading-spinner"></span>
              {{ healthStatus.checking ? '检测中...' : '测试连接' }}
            </button>

            <div v-if="healthStatus.message !== '未检测'" class="test-result" :class="{ 'success': healthStatus.success, 'error': !healthStatus.success }">
              {{ healthStatus.message }}
            </div>
          </div>
        </div>

        <div class="settings-footer">
          <button @click="resetToDefault" class="reset-btn">重置默认</button>
          <div class="footer-actions">
            <button @click="hideSettingsPanel" class="cancel-btn">取消</button>
            <button
              @click="saveSettings"
              :disabled="saveLoading"
              class="save-btn"
            >
              <span v-if="saveLoading" class="loading-spinner"></span>
              {{ saveLoading ? '保存中...' : '保存' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  height: 100%;
}

/* API状态显示 */
.api-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.8rem;
  margin-bottom: 1rem;
  border-radius: 6px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
}

.api-status.status-success {
  border-color: #4CAF50;
  background-color: #e8f5e9;
}

.api-status.status-error {
  border-color: #f44336;
  background-color: #ffebee;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.status-icon {
  font-size: 0.8rem;
}

.status-text {
  color: #333;
}

.checking {
  color: #666;
  font-style: italic;
}

.settings-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.2rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.settings-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.description {
  color: #666;
  margin: 0 0 1rem;
  text-align: center;
  font-size: 0.9rem;
}

.method-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.method-btn {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.method-btn:hover:not(:disabled) {
  border-color: #2196F3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.method-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #f9f9f9;
}

.btn-icon {
  font-size: 1.8rem;
  margin-right: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
}

.btn-content {
  flex: 1;
}

.btn-title {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.2rem;
  color: #333;
}

.btn-desc {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.3;
}

.screenshot-btn .btn-icon {
  color: #4CAF50;
}

.dom-btn .btn-icon {
  color: #FF9800;
}

/* 设置面板样式 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-panel {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.settings-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.settings-content {
  padding: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #2196F3;
  outline: none;
}

.current-url {
  margin: 1rem 0;
  padding: 0.8rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.9rem;
}

.current-url code {
  background-color: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: monospace;
  color: #495057;
}

.test-connection {
  margin-top: 1rem;
}

.test-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn:hover:not(:disabled) {
  background-color: #0b7dda;
}

.test-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.test-result {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.test-result.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.test-result.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.footer-actions {
  display: flex;
  gap: 0.5rem;
}

.reset-btn, .cancel-btn, .save-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
  border: none;
}

.reset-btn:hover {
  background-color: #5a6268;
}

.cancel-btn {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancel-btn:hover {
  background-color: #f8f9fa;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
}

.save-btn:hover:not(:disabled) {
  background-color: #45a049;
}

.save-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>