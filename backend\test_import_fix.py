"""
测试导入修复是否成功
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试各个模块的导入"""
    
    print("🧪 测试模块导入")
    print("=" * 50)
    
    try:
        print("1. 测试 config 导入...")
        from config import config
        print("   ✅ config 导入成功")
        
        print("2. 测试 services.ragflow_service 导入...")
        from services.ragflow_service import default_service
        print("   ✅ ragflow_service 导入成功")
        
        print("3. 测试 routers.health_router 导入...")
        from routers.health_router import router
        print("   ✅ health_router 导入成功")
        
        print("4. 测试 routers.form_router 导入...")
        from routers.form_router import router as form_router
        print("   ✅ form_router 导入成功")
        
        print("5. 测试 main 模块导入...")
        import main
        print("   ✅ main 模块导入成功")
        
        print("\n🎉 所有导入测试通过！")
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n✅ 导入问题已解决！")
    else:
        print("\n❌ 仍有导入问题需要解决")
