"""
策略分析服务
包含策略风险分析和危险端口检测相关功能
"""

from typing import List, Dict, Any
from models.policy_object import PolicyObject, PolicyObjectList


def get_policy_risk_analysis(policy_records: list) -> dict:
    """
    获取策略风险分析结果，返回格式化的数据结构

    Args:
        policy_records: 策略记录列表

    Returns:
        dict: 格式化的风险分析结果
    """
    try:
        # 转换为策略对象列表
        policy_list = PolicyObjectList.from_json_array(policy_records)

        high_risk_policies = []
        medium_risk_policies = []
        safe_policies = []

        # 分析每个策略的风险
        for policy in policy_list:
            try:
                risk_result = policy.check_service_risks()

                if 'error' in risk_result:
                    continue

                # 根据风险级别分类
                if risk_result.get('has_high_risk', False):
                    high_risk_policies.append((policy, risk_result))
                elif risk_result.get('risk_count', 0) > 0:
                    medium_risk_policies.append((policy, risk_result))
                else:
                    safe_policies.append((policy, risk_result))

            except Exception as e:
                continue

        # 收集时间格式错误的详细信息
        time_format_errors = collect_time_format_error_details(policy_list)

        # 时间有效性统计
        expired_policies = []
        expiring_soon_policies = []
        invalid_time_policies = []

        for policy in policy_list:
            try:
                time_validity = policy.check_time_validity()
                if time_validity['time_status'] == 'expired':
                    expired_policies.append((policy, time_validity))
                elif time_validity['time_status'] == 'expiring_soon':
                    expiring_soon_policies.append((policy, time_validity))
                elif time_validity['time_status'] == 'invalid':
                    invalid_time_policies.append((policy, time_validity))
            except Exception as e:
                continue

        # 构建返回结果
        result = {
            "statistics": {
                "total_policies": len(policy_list),
                "high_risk_count": len(high_risk_policies),
                "medium_risk_count": len(medium_risk_policies),
                "safe_count": len(safe_policies),
                "expired_count": len(expired_policies),
                "expiring_soon_count": len(expiring_soon_policies),
                "invalid_time_count": len(invalid_time_policies),
                "time_format_error_count": len(time_format_errors)
            },
            "high_risk_policies": [],
            "medium_risk_policies": [],
            "safe_policies_count": len(safe_policies),
            "time_validity_issues": {
                "expired_policies": [],
                "expiring_soon_policies": [],
                "invalid_time_policies": []
            },
            "time_format_errors": time_format_errors
        }

        # 处理高风险策略
        for i, (policy, risk_result) in enumerate(high_risk_policies, 1):
            # 获取时间有效性信息
            time_validity = policy.check_time_validity()

            high_risk_data = {
                "index": i,
                "policy_id": policy.id,
                "policy_name": policy.name,
                "risk_summary": risk_result['summary'],
                "time_validity": time_validity,
                "prohibited_services": []
            }

            # 收集禁止使用的服务详情
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') in ['high', 'medium']:
                    service_data = {
                        "service": risk_service['service'],
                        "risk_level": risk_service.get('risk_level'),
                        "ports": []
                    }

                    # 添加端口详细信息
                    if risk_service.get('risk_port_details'):
                        for port_detail in risk_service['risk_port_details'][:5]:
                            port_info = {
                                "port": port_detail['port'],
                                "category": port_detail['category'],
                                "risk": port_detail['risk'][:80] + "..." if len(port_detail['risk']) > 80 else port_detail['risk']
                            }
                            service_data['ports'].append(port_info)

                        if len(risk_service['risk_port_details']) > 5:
                            service_data['additional_ports_count'] = len(risk_service['risk_port_details']) - 5

                    # 如果没有详细端口信息，使用原有详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        service_data['general_risk'] = detail[:100] + "..." if len(detail) > 100 else detail

                    high_risk_data['prohibited_services'].append(service_data)

            result['high_risk_policies'].append(high_risk_data)

        # 处理中风险策略
        for i, (policy, risk_result) in enumerate(medium_risk_policies, 1):
            medium_risk_data = {
                "index": i,
                "policy_id": policy.id,
                "policy_name": policy.name,
                "controlled_services": []
            }

            # 收集受控使用的服务详情
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') == 'medium':
                    service_data = {
                        "service": risk_service['service'],
                        "ports": []
                    }

                    # 添加端口详细信息
                    if risk_service.get('risk_port_details'):
                        for port_detail in risk_service['risk_port_details'][:3]:
                            port_info = {
                                "port": port_detail['port'],
                                "category": port_detail['category'],
                                "risk": port_detail['risk'][:60] + "..." if len(port_detail['risk']) > 60 else port_detail['risk']
                            }
                            service_data['ports'].append(port_info)

                        if len(risk_service['risk_port_details']) > 3:
                            service_data['additional_ports_count'] = len(risk_service['risk_port_details']) - 3

                    # 如果没有详细端口信息，使用原有详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        service_data['general_risk'] = detail[:80] + "..." if len(detail) > 80 else detail

                    medium_risk_data['controlled_services'].append(service_data)

            result['medium_risk_policies'].append(medium_risk_data)

        return result

    except Exception as e:
        return {
            "error": f"风险分析处理失败: {str(e)}",
            "statistics": {
                "total_policies": 0,
                "high_risk_count": 0,
                "medium_risk_count": 0,
                "safe_count": 0
            },
            "high_risk_policies": [],
            "medium_risk_policies": [],
            "safe_policies_count": 0
        }


def process_policy_data_with_objects(policy_records: list):
    """
    使用策略对象处理策略数据，重点关注危险端口检测和时间有效性检查

    Args:
        policy_records: 策略记录列表
    """
    print(f"\n🔄 开始处理策略数据，转换为对象数组...")

    try:
        # 转换为策略对象列表
        policy_list = PolicyObjectList.from_json_array(policy_records)

        print(f"✅ 成功转换 {len(policy_list)} 条策略记录为对象")

        # 重点分析危险端口
        try:
            analyze_dangerous_ports(policy_list)
        except Exception as e:
            print(f"⚠️ 危险端口分析时出错: {str(e)}，继续执行时间检查...")

        # 执行时间有效性检查
        try:
            from services.policy_time_service import check_policy_time_validity
            check_policy_time_validity(policy_list)
        except Exception as e:
            print(f"❌ 时间有效性检查时出错: {str(e)}")
            import traceback
            print("详细错误堆栈:")
            traceback.print_exc()

    except Exception as e:
        print(f"❌ 处理策略数据时出错: {str(e)}")
        import traceback
        print("详细错误堆栈:")
        traceback.print_exc()


def collect_time_format_error_details(policy_list: PolicyObjectList) -> list:
    """
    收集时间格式错误策略的详细信息，返回结构化数据

    Args:
        policy_list: 策略对象列表

    Returns:
        list: 时间格式错误策略的详细信息列表
    """
    time_format_errors = []

    for i, policy in enumerate(policy_list):
        try:
            time_result = policy.check_time_validity()
            time_status = time_result.get('time_status')

            # 检查是否为时间格式错误（包括过期策略）
            if time_status in ['invalid', 'expired'] or time_result.get('warnings'):
                error_info = {
                    'index': i + 1,
                    'id': policy.id,
                    'name': policy.name,
                    'start_date': time_result.get('start_date', 'N/A'),
                    'duration': time_result.get('duration', 'N/A'),
                    'end_date': time_result.get('end_date', 'N/A'),
                    'time_status': time_status,
                    'days_until_expiry': time_result.get('days_until_expiry'),
                    'message': time_result.get('time_message', '未知错误'),
                    'warnings': time_result.get('warnings', [])
                }

                # 为过期策略添加特殊标记
                if time_status == 'expired':
                    error_info['message'] = f"策略已过期: {time_result.get('time_message', '')}"
                    if '策略已过期，可能存在时间解析问题' not in error_info['warnings']:
                        error_info['warnings'].append('策略已过期，可能存在时间解析问题')

                time_format_errors.append(error_info)

        except Exception as e:
            # 如果检查时间有效性时出错，也记录为错误
            error_info = {
                'index': i + 1,
                'id': policy.id,
                'name': policy.name,
                'start_date': 'N/A',
                'duration': 'N/A',
                'end_date': 'N/A',
                'time_status': 'error',
                'days_until_expiry': None,
                'message': f'时间检查失败: {str(e)}',
                'warnings': ['时间有效性检查过程中发生异常']
            }
            time_format_errors.append(error_info)

    return time_format_errors


def analyze_dangerous_ports(policy_list: PolicyObjectList):
    """
    分析策略列表中的危险端口

    Args:
        policy_list: 策略对象列表
    """
    print(f"\n🚨 危险端口安全分析报告")
    print("=" * 80)

    high_risk_policies = []
    medium_risk_policies = []
    safe_policies = []

    for policy in policy_list:
        try:
            # 检查服务风险
            risk_result = policy.check_service_risks()

            if 'error' in risk_result:
                print(f"⚠️  策略ID {policy.id}: 风险检测失败 - {risk_result['error']}")
                continue

            # 根据风险级别分类
            if risk_result.get('has_high_risk', False):
                high_risk_policies.append((policy, risk_result))
            elif risk_result.get('risk_count', 0) > 0:
                medium_risk_policies.append((policy, risk_result))
            else:
                safe_policies.append((policy, risk_result))

        except Exception as e:
            print(f"❌ 分析策略ID {policy.id} 时出错: {str(e)}")

    # 打印统计摘要
    total_policies = len(policy_list)
    print(f"📊 策略风险统计:")
    print(f"   总策略数: {total_policies}")
    print(f"   🔴 高风险策略: {len(high_risk_policies)} ({len(high_risk_policies)/total_policies*100:.1f}%)")
    print(f"   🟡 中风险策略: {len(medium_risk_policies)} ({len(medium_risk_policies)/total_policies*100:.1f}%)")
    print(f"   🟢 安全策略: {len(safe_policies)} ({len(safe_policies)/total_policies*100:.1f}%)")

    # 详细分析高风险策略
    if high_risk_policies:
        print(f"\n🔴 高风险策略详情:")
        print("-" * 60)
        for i, (policy, risk_result) in enumerate(high_risk_policies, 1):
            print(f"\n【高风险策略 {i}】")
            print(f"策略ID: {policy.id}")
            print(f"策略名称: {policy.name}")
            # print(f"执行动作: {policy.action}")
            # print(f"命中次数: {policy.hit_count}")

            # 打印危险服务详情
            print(f"禁止使用服务列表:")
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') in ['high', 'medium']:
                    risk_icon = "🔴" if risk_service.get('risk_level') == 'high' else "🟡"
                    # print(f"  {risk_icon} {risk_service['service']}")
                    # print(f"     分类: {risk_service.get('category', 'N/A')}")

                    # 显示端口详细风险信息
                    if risk_service.get('risk_port_details'):
                        # print(f"     端口风险详情:")
                        for port_detail in risk_service['risk_port_details'][:5]:  # 最多显示5个
                            port = port_detail['port']
                            category = port_detail['category']
                            risk = port_detail['risk']

                            print(f"       端口 {port}: {category}")
                            if risk:
                                # 截断过长的风险描述
                                if len(risk) > 80:
                                    risk = risk[:80] + "..."
                                print(f"         风险: {risk}")

                        if len(risk_service['risk_port_details']) > 5:
                            remaining = len(risk_service['risk_port_details']) - 5
                            # print(f"       ... 还有 {remaining} 个端口的详细信息")

                    # 如果没有详细端口信息，显示原有的详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        if len(detail) > 100:
                            detail = detail[:100] + "..."
                        # print(f"     风险: {detail}")

            print(f"风险摘要: {risk_result['summary']}")

    # 详细显示中风险策略
    if medium_risk_policies:
        print(f"\n🟡 中风险策略详情:")
        print("-" * 60)
        for i, (policy, risk_result) in enumerate(medium_risk_policies, 1):
            print(f"\n【中风险策略 {i}】")
            print(f"策略ID: {policy.id}")
            print(f"策略名称: {policy.name}")
            # print(f"执行动作: {policy.action}")
            # print(f"命中次数: {policy.hit_count}")

            # 打印中风险服务详情
            print(f"受控使用服务列表:")
            for risk_service in risk_result.get('risk_services', []):
                if risk_service.get('risk_level') == 'medium':
                    # print(f"  🟡 {risk_service['service']}")
                    # print(f"     分类: {risk_service.get('category', 'N/A')}")

                    # 显示端口详细风险信息
                    if risk_service.get('risk_port_details'):
                        # print(f"     端口风险详情:")
                        for port_detail in risk_service['risk_port_details'][:3]:  # 中风险显示3个
                            port = port_detail['port']
                            category = port_detail['category']
                            risk = port_detail['risk']

                            print(f"       端口 {port}: {category}")
                            if risk:
                                # 截断过长的风险描述
                                if len(risk) > 60:
                                    risk = risk[:60] + "..."
                                print(f"         风险: {risk}")

                        if len(risk_service['risk_port_details']) > 3:
                            remaining = len(risk_service['risk_port_details']) - 3
                            # print(f"       ... 还有 {remaining} 个端口的详细信息")

                    # 如果没有详细端口信息，显示原有的详情
                    elif risk_service.get('details'):
                        detail = risk_service['details'][0]
                        if len(detail) > 80:
                            detail = detail[:80] + "..."
                        # print(f"     风险: {detail}")

            # print(f"风险摘要: {risk_result['summary']}")

    # 安全策略统计
    if safe_policies:
        print(f"\n🟢 安全策略: {len(safe_policies)} 个策略未发现危险端口")

    # 生成安全建议
    # print(f"\n💡 安全建议:")
    # if high_risk_policies:
    #     print(f"   1. 立即审查 {len(high_risk_policies)} 个高风险策略，考虑禁用或限制访问")
    #     print(f"   2. 重点关注包含木马端口、无效端口的策略")
    #     print(f"   3. 检查策略的必要性，移除不必要的危险端口访问")
    #
    # if medium_risk_policies:
    #     print(f"   4. 评估 {len(medium_risk_policies)} 个中风险策略，加强访问控制")
    #     print(f"   5. 对受控使用端口实施更严格的监控和审计")
    #
    # print(f"   6. 定期更新端口风险数据库，保持安全策略的时效性")
    #
    # print("=" * 80)


def process_form_data(form_items: list):
    """
    处理表单数据

    Args:
        form_items: 表单项列表
    """
    print(f"\n📝 处理表单数据")
    print("=" * 60)

    for i, item in enumerate(form_items, 1):
        key = item.get('key', 'N/A')
        value = item.get('value', 'N/A')
        print(f"  [{i:2d}] {key}: {value}")

    print(f"\n总计: {len(form_items)} 个表单字段")


def validate_policy_time_format(policy_name: str) -> dict:
    """
    验证策略名称中的时间格式（独立函数，可单独调用）

    Args:
        policy_name: 策略名称

    Returns:
        dict: 验证结果
    """
    try:
        # 创建临时策略对象进行时间解析
        temp_policy = PolicyObject(name=policy_name)
        return temp_policy.check_time_validity()
    except Exception as e:
        return {
            'has_time_info': False,
            'error': str(e),
            'time_message': f'时间格式验证失败: {str(e)}'
        }
