<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险分析UI测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: bold;
        }
        .mock-data {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        .test-button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }
        .test-button:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 风险分析UI功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">测试说明</div>
            <p>此页面用于测试DomCapture.vue组件中新增的风险分析功能：</p>
            <ul>
                <li>✅ 提交数据按钮现在会调用两个接口：<code>/validate</code> 和 <code>/risk-analysis</code></li>
                <li>✅ 风险分析结果会显示在独立的区域，与原有的修改建议分开</li>
                <li>✅ 支持高风险、中风险、安全策略的分类显示</li>
                <li>✅ 显示详细的端口风险信息和统计数据</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">模拟风险分析数据结构</div>
            <div class="mock-data">
{
  "statistics": {
    "total_policies": 4,
    "high_risk_count": 2,
    "medium_risk_count": 1,
    "safe_count": 1
  },
  "high_risk_policies": [
    {
      "index": 1,
      "policy_id": "HIGH_001",
      "policy_name": "高风险策略-木马端口访问",
      "risk_summary": "服务风险检测: 🔴 高风险: 3个",
      "prohibited_services": [
        {
          "service": "TCP 0",
          "risk_level": "high",
          "ports": [
            {
              "port": 0,
              "category": "禁止使用",
              "risk": "\"0\"是无效端口，通常用于分析操作系统..."
            }
          ]
        },
        {
          "service": "UDP 31337",
          "risk_level": "high",
          "ports": [
            {
              "port": 31337,
              "category": "禁止使用",
              "risk": "Back Orifice木马端口，存在远程控制风险"
            }
          ]
        }
      ]
    }
  ],
  "medium_risk_policies": [
    {
      "index": 1,
      "policy_id": "MED_001",
      "policy_name": "中风险策略-受控服务",
      "controlled_services": [
        {
          "service": "TCP 21",
          "ports": [
            {
              "port": 21,
              "category": "受控使用",
              "risk": "FTP 的传输通道不加密，容易被在传输过程中被窃听..."
            }
          ]
        }
      ]
    }
  ],
  "safe_policies_count": 1
}
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">前端组件修改要点</div>
            <h4>1. 新增状态变量：</h4>
            <ul>
                <li><code>riskAnalysisResult</code> - 存储风险分析结果</li>
                <li><code>riskAnalysisLoading</code> - 风险分析加载状态</li>
                <li><code>riskAnalysisError</code> - 风险分析错误信息</li>
            </ul>

            <h4>2. 修改submitToBackend函数：</h4>
            <ul>
                <li>保持原有的 <code>/validate</code> 接口调用</li>
                <li>对于策略数据，额外调用 <code>/risk-analysis</code> 接口</li>
                <li>分别处理两个接口的响应结果</li>
            </ul>

            <h4>3. 新增UI显示区域：</h4>
            <ul>
                <li>风险统计摘要（总数、高风险、中风险、安全）</li>
                <li>高风险策略详情（禁止使用服务列表）</li>
                <li>中风险策略详情（受控使用服务列表）</li>
                <li>安全策略统计</li>
            </ul>

            <h4>4. 样式设计：</h4>
            <ul>
                <li>橙色主题的风险分析区域</li>
                <li>网格布局的统计数据</li>
                <li>分级颜色标识（红色高风险、橙色中风险、绿色安全）</li>
                <li>详细的端口信息展示</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">测试步骤</div>
            <ol>
                <li>在防火墙管理页面打开浏览器插件</li>
                <li>解析包含策略数据的页面</li>
                <li>点击"提交数据"按钮</li>
                <li>观察是否同时显示：
                    <ul>
                        <li>原有的修改建议（蓝色区域）</li>
                        <li>新的风险分析报告（橙色区域）</li>
                    </ul>
                </li>
                <li>检查风险分析报告的各个部分是否正确显示</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">预期效果</div>
            <div style="background-color: #fff3e0; padding: 15px; border: 2px solid #ff9800; border-radius: 8px;">
                <h3 style="color: #e65100; margin-top: 0;">🛡️ 端口风险分析报告</h3>
                
                <div style="background-color: #f5f5f5; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <h4 style="color: #bf360c; margin-top: 0;">📊 端口统计</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 0.8rem;">
                        <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: white; border-left: 4px solid #ccc; border-radius: 4px;">
                            <span>总策略数：</span><strong>4</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: #ffebee; border-left: 4px solid #f44336; border-radius: 4px;">
                            <span>🔴 高风险：</span><strong>2</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: #fff3e0; border-left: 4px solid #ff9800; border-radius: 4px;">
                            <span>🟡 中风险：</span><strong>1</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: #e8f5e8; border-left: 4px solid #4caf50; border-radius: 4px;">
                            <span>🟢 安全：</span><strong>1</strong>
                        </div>
                    </div>
                </div>

                <h4 style="color: #bf360c;">🔴 高风险策略详情</h4>
                <div style="background-color: #ffebee; padding: 1rem; border: 1px solid #f44336; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <strong>【高风险策略 1】</strong>
                        <span style="background: #f0f0f0; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.9rem;">ID: HIGH_001</span>
                    </div>
                    <div style="font-style: italic; margin-bottom: 1rem;">高风险策略-木马端口访问</div>
                    <div>
                        <strong>禁止使用服务列表：</strong>
                        <div style="margin-top: 0.5rem; padding: 0.8rem; background: white; border: 1px solid #e0e0e0; border-radius: 4px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                <span style="font-weight: 500;">TCP 0</span>
                                <span style="background: #f44336; color: white; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">🔴 高风险</span>
                            </div>
                            <div style="background: #f9f9f9; padding: 0.5rem; border-left: 3px solid #2196f3; border-radius: 3px;">
                                <div style="font-weight: 500; margin-bottom: 0.3rem;"><strong>端口 0:</strong> 禁止使用</div>
                                <div style="font-size: 0.9rem; color: #666;">风险: "0"是无效端口，通常用于分析操作系统...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
