"""
报告服务
包含各种打印和报告生成相关功能
"""

from typing import List, Dict, Any


def print_expired_policies_details(expired_policies: list):
    """
    打印已过期策略的详细信息
    """
    print(f"\n❌ 已过期策略详情 (共 {len(expired_policies)} 个):")
    print("-" * 80)

    for i, policy in enumerate(expired_policies[:10], 1):  # 最多显示10个
        print(f"{i}. 策略ID: {policy['id']}")
        print(f"   策略名称: {policy['name'][:80]}{'...' if len(policy['name']) > 80 else ''}")
        print(f"   开始日期: {policy['start_date']}")
        print(f"   结束日期: {policy['end_date']}")
        print(f"   状态: {policy['message']}")

        if policy['warnings']:
            print(f"   警告: {'; '.join(policy['warnings'])}")
        print()

    if len(expired_policies) > 10:
        print(f"   ... 还有 {len(expired_policies) - 10} 个已过期策略未显示")


def print_expiring_soon_policies_details(expiring_policies: list):
    """
    打印即将过期策略的详细信息
    """
    print(f"\n⚠️  即将过期策略详情 (共 {len(expiring_policies)} 个):")
    print("-" * 80)

    # 按剩余天数排序
    expiring_policies_sorted = sorted(expiring_policies, key=lambda x: x['days_until_expiry'])

    for i, policy in enumerate(expiring_policies_sorted[:10], 1):  # 最多显示10个
        print(f"{i}. 策略ID: {policy['id']}")
        print(f"   策略名称: {policy['name'][:80]}{'...' if len(policy['name']) > 80 else ''}")
        print(f"   开始日期: {policy['start_date']}")
        print(f"   结束日期: {policy['end_date']}")
        print(f"   剩余天数: {policy['days_until_expiry']} 天")
        print(f"   状态: {policy['message']}")
        print()

    if len(expiring_policies) > 10:
        print(f"   ... 还有 {len(expiring_policies) - 10} 个即将过期策略未显示")


def print_invalid_time_policies_details(invalid_policies: list):
    """
    打印时间格式错误策略的详细信息
    """
    print(f"\n🔧 时间格式错误策略详情 (共 {len(invalid_policies)} 个):")
    print("-" * 80)

    for i, policy in enumerate(invalid_policies[:10], 1):  # 最多显示10个
        print(f"{i}. 策略ID: {policy['id']}")
        print(f"   策略名称: {policy['name'][:80]}{'...' if len(policy['name']) > 80 else ''}")
        print(f"   解析的开始日期: {policy['start_date']}")
        print(f"   解析的持续时间: {policy['duration']}")
        print(f"   错误信息: {policy['message']}")

        if policy['warnings']:
            print(f"   警告: {'; '.join(policy['warnings'])}")
        print()

    if len(invalid_policies) > 10:
        print(f"   ... 还有 {len(invalid_policies) - 10} 个时间格式错误策略未显示")


def print_time_format_error_details(error_list: list):
    """
    打印时间格式错误策略的详细信息

    Args:
        error_list: 错误策略列表
    """
    print(f"\n🔧 策略时间错误详细信息 (共 {len(error_list)} 个):")
    print("=" * 100)

    for i, error_info in enumerate(error_list, 1):
        # print(f"\n【错误策略 {i}】")
        # print(f"序号: {error_info['index']}")
        print(f"策略ID: {error_info['id']}")
        print(f"策略名称: {error_info['name']}")
        print(f"解析的起始时间: {error_info['start_date']}")
        print(f"解析的持续时间: {error_info['duration']}")
        # print(f"时间状态: {error_info['time_status']}")
        print(f"错误原因: {error_info['message']}")

        if error_info['warnings']:
            print(f"具体警告:")
            for j, warning in enumerate(error_info['warnings'], 1):
                print(f"  {j}. {warning}")

        print("-" * 60)
