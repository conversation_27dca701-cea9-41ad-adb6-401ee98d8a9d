export default defineBackground(() => {
  console.log('Screenshot Extension Background Started', { id: browser.runtime.id });

  // 监听来自popup的消息
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'captureScreen') {
      captureCurrentTab()
        .then(dataUrl => {
          sendResponse({ success: true, dataUrl });
        })
        .catch(error => {
          console.error('截图失败:', error);
          sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
        });
      
      // 返回true表示将异步发送响应
      return true;
    }
  });
});

// 定义截图选项的类型
interface CaptureOptions {
  format?: 'jpeg' | 'png';
  quality?: number;
}

// 捕获当前标签页的屏幕
async function captureCurrentTab() {
  try {
    // 获取当前标签页
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      throw new Error('无法获取当前标签页');
    }
    
    // 捕获屏幕 - 使用当前窗口ID
    const currentWindowId = tabs[0].windowId;
    
    // 对于特殊页面（如新标签页、空白页或扩展页面），将使用不同的截图方式
    const url = tabs[0].url || '';
    const isSpecialPage = 
      url === '' || 
      url === 'about:blank' || 
      url.startsWith('chrome://') || 
      url.startsWith('chrome-extension://') ||
      url.startsWith('edge://') ||
      url.startsWith('about:');
    
    // 如果是特殊页面，不指定格式选项
    const options: CaptureOptions = isSpecialPage ? {} : { format: 'png' };
    
    return await browser.tabs.captureVisibleTab(currentWindowId, options);
  } catch (err) {
    console.error('截图过程中出错:', err);
    
    // 如果特定方式失败，尝试不带任何参数的截图（最简方式）
    try {
      const windowInfo = await browser.windows.getCurrent();
      if (windowInfo.id) {
        return await browser.tabs.captureVisibleTab(windowInfo.id);
      } else {
        throw new Error('无法获取当前窗口');
      }
    } catch (fallbackError) {
      console.error('备选截图方式也失败:', fallbackError);
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new Error(`无法截取当前页面: ${errorMessage}`);
    }
  }
}
