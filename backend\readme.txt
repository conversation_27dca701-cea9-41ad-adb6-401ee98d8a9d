安装环境：
pip install fastapi
pip install uvicorn
pip install ragflow_sdk
pip install python-multipart
pip install pillow
openpyxl
pandas
cv2

启动Fastapi:
uvicorn main:app --reload --log-level debug --port 8001

在电脑window环境下命令行输入测试命令：
curl -X POST "http://localhost:8000/form/validate" ^
-H "Content-Type: application/json" ^
-d "{\"items\": [{\"key\": \"申请部门\", \"value\": \"信息通信分公司（数据中心）\"},{\"key\": \"申请日期\", \"value\": \"2025-05-21\"},{\"key\": \"申请人\", \"value\": \"苏竞成\"},{\"key\": \"申请人联系电话\", \"value\": \"17557938006\"},{\"key\": \"目的运行环境\", \"value\": \"张三\"},{\"key\": \"源类型\", \"value\": \"办公终端\"},{\"key\": \"有效期\", \"value\": \"2025-6-10\"},{\"key\": \"至\", \"value\": \"2025-7-10\"},{\"key\": \"源地址\", \"value\": \"************\"},{\"key\": \"源端口\", \"value\": \"9001\"},{\"key\": \"目的地址\", \"value\": \"************\"},{\"key\": \"目的端口\", \"value\": \"10039\"},{\"key\": \"端口类型\", \"value\": \"TCP\"},{\"key\": \"源地址描述\", \"value\": \"江苏通信公司5楼栋3层办公终端\"},{\"key\": \"目的地址描述\", \"value\": \"通信系统信号模块\"},{\"key\": \"备注\", \"value\": \"\"}]}"
