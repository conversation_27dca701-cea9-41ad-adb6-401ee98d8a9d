"""
防火墙策略对象模型
用于将JSON数据转换为结构化对象，并提供详细的字符串输出
"""

from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass, field
import re
from datetime import datetime, timedelta
from models.policy_name_parser import PolicyNameParser, PolicyNameInfo


@dataclass
class PolicyObject:
    """防火墙策略对象"""
    
    # 基础字段
    id: str = ""
    name: str = ""
    src_zone: str = ""
    src_addr: List[str] = field(default_factory=list)
    dst_zone: str = ""
    dst_addr: List[str] = field(default_factory=list)
    service: List[str] = field(default_factory=list)
    action: str = ""
    hit_count: str = ""
    
    # 名称解析结果（冗余存储）
    name_parsed: Optional[PolicyNameInfo] = None
    name_start_date: Optional[str] = None
    name_source: Optional[str] = None
    name_target: Optional[str] = None
    name_owner: Optional[str] = None
    name_duration: Optional[str] = None
    name_category: Optional[str] = None
    name_system_name: Optional[str] = None
    name_policy_type: Optional[str] = None
    name_confidence: float = 0.0
    
    # 统计信息
    src_addr_count: int = 0
    dst_addr_count: int = 0
    service_count: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        # 解析策略名称
        if self.name:
            parser = PolicyNameParser()
            self.name_parsed = parser.parse(self.name)
            
            # 冗余存储解析结果
            self.name_start_date = self.name_parsed.start_date
            self.name_source = self.name_parsed.source
            self.name_target = self.name_parsed.target
            self.name_owner = self.name_parsed.owner
            self.name_duration = self.name_parsed.duration
            self.name_category = self.name_parsed.category
            self.name_system_name = self.name_parsed.system_name
            self.name_policy_type = self.name_parsed.policy_type.value if self.name_parsed.policy_type else None
            self.name_confidence = self.name_parsed.confidence
        
        # 计算统计信息
        self.src_addr_count = len(self.src_addr) if isinstance(self.src_addr, list) else 0
        self.dst_addr_count = len(self.dst_addr) if isinstance(self.dst_addr, list) else 0
        self.service_count = len(self.service) if isinstance(self.service, list) else 0
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PolicyObject':
        """从字典创建策略对象"""
        # 处理可能的数据类型转换
        src_addr = data.get('src_addr', [])
        if isinstance(src_addr, str):
            src_addr = [src_addr] if src_addr else []
        elif not isinstance(src_addr, list):
            src_addr = []
        
        dst_addr = data.get('dst_addr', [])
        if isinstance(dst_addr, str):
            dst_addr = [dst_addr] if dst_addr else []
        elif not isinstance(dst_addr, list):
            dst_addr = []
        
        service = data.get('service', [])
        if isinstance(service, str):
            service = [service] if service else []
        elif not isinstance(service, list):
            service = []
        
        return cls(
            id=str(data.get('id', '')),
            name=str(data.get('name', '')),
            src_zone=str(data.get('src_zone', '')),
            src_addr=src_addr,
            dst_zone=str(data.get('dst_zone', '')),
            dst_addr=dst_addr,
            service=service,
            action=str(data.get('action', '')),
            hit_count=str(data.get('hit_count', ''))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'src_zone': self.src_zone,
            'src_addr': self.src_addr,
            'dst_zone': self.dst_zone,
            'dst_addr': self.dst_addr,
            'service': self.service,
            'action': self.action,
            'hit_count': self.hit_count,
            'name_parsed': {
                'start_date': self.name_start_date,
                'source': self.name_source,
                'target': self.name_target,
                'owner': self.name_owner,
                'duration': self.name_duration,
                'category': self.name_category,
                'system_name': self.name_system_name,
                'policy_type': self.name_policy_type,
                'confidence': self.name_confidence
            },
            'statistics': {
                'src_addr_count': self.src_addr_count,
                'dst_addr_count': self.dst_addr_count,
                'service_count': self.service_count
            }
        }
    
    def __str__(self) -> str:
        """详细的字符串表示"""
        lines = []
        lines.append(f"🔹 策略ID: {self.id}")
        lines.append(f"📝 策略名称: {self.name}")
        
        # 名称解析结果
        if self.name_policy_type:
            lines.append(f"🏷️  名称解析:")
            lines.append(f"   类型: {self.name_policy_type} (置信度: {self.name_confidence:.2f})")
            
            if self.name_start_date:
                lines.append(f"   📅 开始日期: {self.name_start_date}")
            if self.name_source:
                lines.append(f"   🏢 来源系统: {self.name_source}")
            if self.name_target:
                lines.append(f"   🎯 目标系统: {self.name_target}")
            if self.name_owner:
                lines.append(f"   👤 负责人: {self.name_owner}")
            if self.name_duration:
                lines.append(f"   ⏰ 期限: {self.name_duration}")
            if self.name_category:
                lines.append(f"   📂 分类: {self.name_category}")
            if self.name_system_name:
                lines.append(f"   🚨 系统: {self.name_system_name}")
        
        # 基础信息
        lines.append(f"🛡️  安全域:")
        lines.append(f"   源: {self.src_zone}")
        lines.append(f"   目的: {self.dst_zone}")
        
        # 地址信息
        lines.append(f"📍 地址信息:")
        lines.append(f"   源地址 ({self.src_addr_count}个): {self._format_list(self.src_addr, 3)}")
        lines.append(f"   目的地址 ({self.dst_addr_count}个): {self._format_list(self.dst_addr, 3)}")
        
        # 服务信息
        lines.append(f"🔧 服务类型 ({self.service_count}个): {self._format_list(self.service, 5)}")

        # 服务风险检测
        if self.service:
            risk_result = self.check_service_risks()
            if 'error' not in risk_result:
                lines.append(f"🛡️  服务风险: {risk_result['summary']}")
                if risk_result['has_high_risk']:
                    lines.append(f"   ⚠️  发现高风险服务，建议详细检查")

        # 动作和统计
        lines.append(f"⚡ 执行动作: {self.action}")
        lines.append(f"📊 命中次数: {self.hit_count}")

        return '\n'.join(lines)

    def check_time_validity(self) -> Dict[str, Any]:
        """
        检查策略的时间有效性

        Returns:
            Dict: 时间有效性检查结果
        """
        result = {
            'has_time_info': False,
            'start_date': None,
            'duration': None,
            'end_date': None,
            'is_valid': None,
            'is_expired': None,
            'days_until_expiry': None,
            'time_status': 'unknown',
            'time_message': '',
            'warnings': []
        }

        try:
            # 检查是否有时间信息
            if not self.name_start_date and not self.name_duration:
                result['time_message'] = '策略名称中未包含时间信息'
                return result

            result['has_time_info'] = True
            result['start_date'] = self.name_start_date
            result['duration'] = self.name_duration

            # 解析开始日期
            start_date = self._parse_date(self.name_start_date)
            if not start_date:
                result['warnings'].append(f'无法解析开始日期: {self.name_start_date}')
                result['time_status'] = 'invalid'
                result['time_message'] = '开始日期格式无效'
                return result

            # 解析持续时间/结束日期
            end_date = self._parse_duration_or_end_date(self.name_duration, start_date)
            if not end_date:
                if self.name_duration and self.name_duration.lower() not in ['长期', '永久', '无限期']:
                    result['warnings'].append(f'无法解析持续时间: {self.name_duration}')
                    result['time_status'] = 'invalid'
                    result['time_message'] = '持续时间格式无效'
                else:
                    result['time_status'] = 'unknown'
                    result['time_message'] = '长期策略或持续时间格式无效'
                return result

            result['end_date'] = end_date.strftime('%Y-%m-%d')

            # 获取当前时间
            current_time = datetime.now()

            # 检查时间逻辑
            if end_date <= start_date:
                result['is_valid'] = False
                result['time_status'] = 'invalid'
                result['time_message'] = '结束时间早于或等于开始时间'
                result['warnings'].append('时间逻辑错误：结束时间应晚于开始时间')
                return result

            # 检查是否过期
            if end_date < current_time:
                result['is_expired'] = True
                result['is_valid'] = False
                result['time_status'] = 'expired'
                expired_days = (current_time - end_date).days
                result['days_until_expiry'] = -expired_days  # 负数表示已过期天数
                result['time_message'] = f'策略已过期 {expired_days} 天'
            else:
                result['is_expired'] = False
                result['is_valid'] = True
                result['time_status'] = 'active'
                days_until_expiry = (end_date - current_time).days
                result['days_until_expiry'] = days_until_expiry

                if days_until_expiry <= 7:
                    result['time_status'] = 'expiring_soon'
                    result['time_message'] = f'策略将在 {days_until_expiry} 天后过期'
                    result['warnings'].append('策略即将过期，建议及时更新')
                elif days_until_expiry <= 30:
                    result['time_message'] = f'策略将在 {days_until_expiry} 天后过期'
                else:
                    result['time_message'] = f'策略有效，还有 {days_until_expiry} 天到期'

            return result

        except Exception as e:
            result['time_status'] = 'invalid'
            result['time_message'] = f'时间检查出错: {str(e)}'
            result['warnings'].append(f'时间检查异常: {str(e)}')
            return result

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """
        解析日期字符串，支持多种格式包括6位简写格式

        Args:
            date_str: 日期字符串

        Returns:
            datetime: 解析后的日期对象，失败返回None
        """
        if not date_str:
            return None

        date_str = date_str.strip()

        # 优先处理6位简写日期格式 (YYMMDD -> 20YYMMDD 或 9999MMDD)
        # 这样可以避免被8位格式错误解析
        if len(date_str) == 6 and date_str.isdigit():
            try:
                # 提取年月日
                year_part = date_str[:2]
                month_part = date_str[2:4]
                day_part = date_str[4:6]

                # 转换为完整年份
                # 如果前两位是99，则是9999年（表示永远）
                # 否则是20XX年（21世纪）
                if year_part == "99":
                    full_year = 9999
                else:
                    full_year = 2000 + int(year_part)

                # 构建完整日期字符串
                full_date_str = f"{full_year}{month_part}{day_part}"
                return datetime.strptime(full_date_str, '%Y%m%d')

            except ValueError:
                pass

        # 常见日期格式（只处理8位及以上的日期）
        date_formats = [
            '%Y%m%d',  # 20220519 (8位)
            '%Y-%m-%d',  # 2022-05-19
            '%Y/%m/%d',  # 2022/05/19
            '%Y.%m.%d',  # 2022.05.19
            '%m/%d/%Y',  # 05/19/2022
            '%d/%m/%Y',  # 19/05/2022
        ]

        # 尝试标准格式（但跳过6位数字，因为已经在上面处理了）
        for fmt in date_formats:
            try:
                # 对于 %Y%m%d 格式，确保至少是8位数字
                if fmt == '%Y%m%d' and len(date_str) < 8:
                    continue
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        return None

    def _parse_duration_or_end_date(self, duration_str: str, start_date: datetime) -> Optional[datetime]:
        """
        解析持续时间或结束日期，支持6位日期格式

        Args:
            duration_str: 持续时间字符串
            start_date: 开始日期

        Returns:
            datetime: 计算后的结束日期，失败返回None
        """
        if not duration_str:
            return None

        duration_str = duration_str.strip().lower()

        # 长期策略
        if duration_str in ['长期', '永久', '无限期', 'permanent', 'forever']:
            return None

        # 优先尝试解析为具体日期（包括6位格式）
        end_date = self._parse_date(duration_str)
        if end_date:
            return end_date

        # 如果不是日期格式，再尝试解析为时间段
        try:
            import re

            # 年
            year_match = re.search(r'(\d+)\s*年', duration_str)
            if year_match:
                years = int(year_match.group(1))
                return start_date.replace(year=start_date.year + years)

            # 月
            month_match = re.search(r'(\d+)\s*月', duration_str)
            if month_match:
                months = int(month_match.group(1))
                # 简单的月份计算
                new_month = start_date.month + months
                new_year = start_date.year + (new_month - 1) // 12
                new_month = ((new_month - 1) % 12) + 1
                return start_date.replace(year=new_year, month=new_month)

            # 天
            day_match = re.search(r'(\d+)\s*天', duration_str)
            if day_match:
                days = int(day_match.group(1))
                return start_date + timedelta(days=days)

            # 周
            week_match = re.search(r'(\d+)\s*周', duration_str)
            if week_match:
                weeks = int(week_match.group(1))
                return start_date + timedelta(weeks=weeks)

            # 英文格式
            if 'year' in duration_str:
                year_match = re.search(r'(\d+)\s*year', duration_str)
                if year_match:
                    years = int(year_match.group(1))
                    return start_date.replace(year=start_date.year + years)

            if 'month' in duration_str:
                month_match = re.search(r'(\d+)\s*month', duration_str)
                if month_match:
                    months = int(month_match.group(1))
                    new_month = start_date.month + months
                    new_year = start_date.year + (new_month - 1) // 12
                    new_month = ((new_month - 1) % 12) + 1
                    return start_date.replace(year=new_year, month=new_month)

            if 'day' in duration_str:
                day_match = re.search(r'(\d+)\s*day', duration_str)
                if day_match:
                    days = int(day_match.group(1))
                    return start_date + timedelta(days=days)

        except Exception:
            pass

        return None

    def _format_list(self, items: List[str], max_display: int = 3) -> str:
        """格式化列表显示"""
        if not items:
            return "无"
        
        if len(items) <= max_display:
            return ', '.join(items)
        else:
            displayed = ', '.join(items[:max_display])
            return f"{displayed} ... (还有{len(items) - max_display}个)"
    
    def get_summary(self) -> str:
        """获取简要摘要"""
        return (f"策略{self.id}: {self.name_policy_type or '未知类型'} | "
                f"动作:{self.action} | 命中:{self.hit_count} | "
                f"地址:{self.src_addr_count}→{self.dst_addr_count} | "
                f"服务:{self.service_count}个")
    
    def get_name_analysis(self) -> str:
        """获取名称分析详情"""
        if not self.name_policy_type:
            return f"名称: {self.name} (未能解析)"
        
        parts = [f"类型:{self.name_policy_type}"]
        
        if self.name_start_date:
            parts.append(f"日期:{self.name_start_date}")
        if self.name_source:
            parts.append(f"来源:{self.name_source}")
        if self.name_target:
            parts.append(f"目标:{self.name_target}")
        if self.name_owner:
            parts.append(f"负责人:{self.name_owner}")
        if self.name_duration:
            parts.append(f"期限:{self.name_duration}")
        if self.name_category:
            parts.append(f"分类:{self.name_category}")
        
        return f"名称解析: {' | '.join(parts)} (置信度:{self.name_confidence:.2f})"

    def check_service_risks(self) -> Dict[str, Any]:
        """
        检查服务列表中的风险端口

        Returns:
            Dict: 风险检测结果
        """
        try:
            # 导入端口风险映射表
            from port_risk_map import PORT_RISK_MAP
        except ImportError:
            return {
                'error': '无法导入端口风险映射表',
                'total_services': len(self.service),
                'risk_services': [],
                'safe_services': [],
                'unknown_services': []
            }

        risk_services = []
        safe_services = []
        unknown_services = []

        for service in self.service:
            service = service.strip()
            if not service:
                continue

            # 解析服务字符串
            parsed_services = self._parse_service_string(service)

            for parsed_service in parsed_services:
                risk_info = self._check_single_service_risk(parsed_service, PORT_RISK_MAP)

                if risk_info['has_risk']:
                    risk_services.append(risk_info)
                elif risk_info['is_safe']:
                    safe_services.append(risk_info)
                else:
                    unknown_services.append(risk_info)

        return {
            'total_services': len(self.service),
            'risk_count': len(risk_services),
            'safe_count': len(safe_services),
            'unknown_count': len(unknown_services),
            'risk_services': risk_services,
            'safe_services': safe_services,
            'unknown_services': unknown_services,
            'has_high_risk': any(rs.get('risk_level') == 'high' for rs in risk_services),
            'summary': self._generate_risk_summary(risk_services, safe_services, unknown_services)
        }

    def _parse_service_string(self, service: str) -> List[Dict[str, Any]]:
        """
        解析服务字符串，支持多种格式

        Args:
            service: 服务字符串

        Returns:
            List[Dict]: 解析后的服务列表
        """
        service = service.strip().upper()

        # 处理特殊情况
        if service in ['ICMP', 'ANY']:
            return [{
                'original': service,
                'protocol': service,
                'port': None,
                'port_range': None,
                'type': 'special'
            }]

        parsed_services = []

        # 正则表达式匹配不同格式
        patterns = [
            # TCP/UDP 端口范围: UDP 50000-60000, TCP_5432
            r'^(TCP|UDP)[_\s]+(\d+)-(\d+)$',
            r'^(TCP|UDP)\s+(\d+)-(\d+)$',
            # 单个端口: TCP 5558, TCP_5432
            r'^(TCP|UDP)[_\s]+(\d+)$',
            r'^(TCP|UDP)\s+(\d+)$',
            # 纯端口号
            r'^(\d+)$'
        ]

        matched = False

        for pattern in patterns:
            match = re.match(pattern, service)
            if match:
                matched = True
                groups = match.groups()

                if len(groups) == 3:  # 端口范围
                    protocol, start_port, end_port = groups
                    parsed_services.append({
                        'original': service,
                        'protocol': protocol,
                        'port': None,
                        'port_range': (int(start_port), int(end_port)),
                        'type': 'range'
                    })
                elif len(groups) == 2:  # 单个端口
                    protocol, port = groups
                    parsed_services.append({
                        'original': service,
                        'protocol': protocol,
                        'port': int(port),
                        'port_range': None,
                        'type': 'single'
                    })
                elif len(groups) == 1:  # 纯端口号，假设为TCP
                    port = groups[0]
                    parsed_services.append({
                        'original': service,
                        'protocol': 'TCP',
                        'port': int(port),
                        'port_range': None,
                        'type': 'single'
                    })
                break

        if not matched:
            # 无法解析的服务
            parsed_services.append({
                'original': service,
                'protocol': 'UNKNOWN',
                'port': None,
                'port_range': None,
                'type': 'unknown'
            })

        return parsed_services

    def _check_single_service_risk(self, parsed_service: Dict[str, Any], port_risk_map: Dict[str, str]) -> Dict[str, Any]:
        """
        检查单个服务的风险

        Args:
            parsed_service: 解析后的服务信息
            port_risk_map: 端口风险映射表

        Returns:
            Dict: 风险检测结果
        """
        result = {
            'service': parsed_service['original'],
            'protocol': parsed_service['protocol'],
            'port': parsed_service['port'],
            'port_range': parsed_service['port_range'],
            'type': parsed_service['type'],
            'has_risk': False,
            'is_safe': False,
            'risk_level': 'unknown',
            'risk_info': '',
            'category': '',
            'details': []
        }

        # 处理特殊情况
        if parsed_service['type'] == 'special':
            if parsed_service['protocol'] in ['ICMP', 'ANY']:
                result['is_safe'] = True
                result['risk_level'] = 'safe'
                result['risk_info'] = f"{parsed_service['protocol']} 协议，暂时通过"
                return result

        # 处理未知格式
        if parsed_service['type'] == 'unknown':
            result['risk_info'] = '无法解析的服务格式'
            return result

        # 处理单个端口
        if parsed_service['type'] == 'single':
            return self._check_single_port_risk(parsed_service, port_risk_map, result)

        # 处理端口范围
        if parsed_service['type'] == 'range':
            return self._check_port_range_risk(parsed_service, port_risk_map, result)

        return result

    def _check_single_port_risk(self, parsed_service: Dict[str, Any], port_risk_map: Dict[str, str], result: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查单个端口的风险
        """
        protocol = parsed_service['protocol']
        port = parsed_service['port']

        # 构建查询键
        key = f"{protocol}:{port}"

        if key in port_risk_map:
            risk_info = port_risk_map[key]
            result['has_risk'] = True
            result['risk_info'] = risk_info

            # 解析分类和风险
            if '|' in risk_info:
                category, risk_detail = risk_info.split('|', 1)
                result['category'] = category.strip()
                result['details'] = [risk_detail.strip()]

                # 添加单个端口的详细信息
                result['risk_port_details'] = [{
                    'port': port,
                    'category': category.strip(),
                    'risk': risk_detail.strip()
                }]

                # 判断风险级别
                if '禁止使用' in category:
                    result['risk_level'] = 'high'
                elif '受控使用' in category:
                    result['risk_level'] = 'medium'
                else:
                    result['risk_level'] = 'low'
            else:
                result['category'] = risk_info
                result['risk_level'] = 'medium'
                result['risk_port_details'] = [{
                    'port': port,
                    'category': risk_info,
                    'risk': ''
                }]
        else:
            # 端口不在风险映射表中
            result['is_safe'] = True
            result['risk_level'] = 'safe'
            result['risk_info'] = f"{protocol}:{port} 未在风险端口列表中"

        return result

    def _check_port_range_risk(self, parsed_service: Dict[str, Any], port_risk_map: Dict[str, str], result: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查端口范围的风险
        """
        protocol = parsed_service['protocol']
        start_port, end_port = parsed_service['port_range']

        # 特殊范围检查
        risk_ports = []

        # TCP端口范围检查：从哈希表中逐个检查
        if protocol == 'TCP':
            # 限制检查范围，避免性能问题
            check_limit = 10000  # 最多检查10000个端口
            actual_end = min(end_port, start_port + check_limit - 1)

            if end_port - start_port > check_limit:
                result['has_risk'] = True
                result['risk_level'] = 'high'
                result['risk_info'] = f"TCP端口范围过大 ({end_port - start_port + 1} 个端口)，超出检查限制"
                result['category'] = '禁止使用'
                result['details'] = [f"端口范围过大，建议缩小到 {check_limit} 个端口以内"]
                return result

            # 逐个检查TCP端口，收集详细风险信息
            tcp_risk_ports = []
            tcp_risk_details = []

            for port in range(start_port, actual_end + 1):
                key = f"TCP:{port}"
                if key in port_risk_map:
                    tcp_risk_ports.append(port)
                    risk_info = port_risk_map[key]

                    # 解析风险信息
                    if '|' in risk_info:
                        category, risk_detail = risk_info.split('|', 1)
                        tcp_risk_details.append({
                            'port': port,
                            'category': category.strip(),
                            'risk': risk_detail.strip()[:100] + ('...' if len(risk_detail.strip()) > 100 else '')
                        })
                    else:
                        tcp_risk_details.append({
                            'port': port,
                            'category': risk_info,
                            'risk': ''
                        })

            if tcp_risk_ports:
                result['has_risk'] = True
                result['risk_level'] = 'high'
                result['risk_info'] = f"TCP端口范围 {start_port}-{actual_end} 包含 {len(tcp_risk_ports)} 个风险端口"
                result['category'] = '禁止使用'

                # 构建详细信息
                port_list = ', '.join(map(str, tcp_risk_ports[:10]))
                if len(tcp_risk_ports) > 10:
                    port_list += f'... (共{len(tcp_risk_ports)}个)'

                result['details'] = [f"发现TCP风险端口: {port_list}"]
                result['risk_port_details'] = tcp_risk_details[:10]  # 最多显示10个详细信息
            else:
                result['is_safe'] = True
                result['risk_level'] = 'safe'
                result['risk_info'] = f"TCP端口范围 {start_port}-{actual_end} 未发现风险端口"

        elif protocol == 'UDP':
            # UDP端口范围检查：结合特殊端口和哈希表查询
            udp_risk_ports = []

            # 限制检查范围，避免性能问题
            check_limit = 10000  # 最多检查10000个UDP端口

            if end_port - start_port > check_limit:
                result['has_risk'] = True
                result['risk_level'] = 'high'
                result['risk_info'] = f"UDP端口范围过大 ({end_port - start_port + 1} 个端口)，超出检查限制"
                result['category'] = '禁止使用'
                result['details'] = [f"端口范围过大，建议缩小到 {check_limit} 个端口以内"]
                return result

            # 收集UDP风险端口和详细信息
            udp_risk_ports = []
            udp_risk_details = []

            # 优先检查已知的高风险UDP端口
            known_risk_ports = [54321, 47262, 30003, 30100, 30303, 30999, 31337, 31338, 31339, 31666, 31789]
            for port in known_risk_ports:
                if start_port <= port <= end_port:
                    udp_risk_ports.append(port)
                    key = f"UDP:{port}"
                    if key in port_risk_map:
                        risk_info = port_risk_map[key]
                        if '|' in risk_info:
                            category, risk_detail = risk_info.split('|', 1)
                            udp_risk_details.append({
                                'port': port,
                                'category': category.strip(),
                                'risk': risk_detail.strip()[:100] + ('...' if len(risk_detail.strip()) > 100 else '')
                            })
                        else:
                            udp_risk_details.append({
                                'port': port,
                                'category': risk_info,
                                'risk': ''
                            })

            # 对于较小的范围，逐个检查哈希表
            if end_port - start_port <= 1000:  # 小范围直接查询哈希表
                for port in range(start_port, end_port + 1):
                    key = f"UDP:{port}"
                    if key in port_risk_map and port not in udp_risk_ports:
                        udp_risk_ports.append(port)
                        risk_info = port_risk_map[key]
                        if '|' in risk_info:
                            category, risk_detail = risk_info.split('|', 1)
                            udp_risk_details.append({
                                'port': port,
                                'category': category.strip(),
                                'risk': risk_detail.strip()[:100] + ('...' if len(risk_detail.strip()) > 100 else '')
                            })
                        else:
                            udp_risk_details.append({
                                'port': port,
                                'category': risk_info,
                                'risk': ''
                            })
            else:
                # 大范围只检查30000以下的端口（哈希表中主要是这些）
                for port in range(max(0, start_port), min(30000, end_port + 1)):
                    key = f"UDP:{port}"
                    if key in port_risk_map and port not in udp_risk_ports:
                        udp_risk_ports.append(port)
                        risk_info = port_risk_map[key]
                        if '|' in risk_info:
                            category, risk_detail = risk_info.split('|', 1)
                            udp_risk_details.append({
                                'port': port,
                                'category': category.strip(),
                                'risk': risk_detail.strip()[:100] + ('...' if len(risk_detail.strip()) > 100 else '')
                            })
                        else:
                            udp_risk_details.append({
                                'port': port,
                                'category': risk_info,
                                'risk': ''
                            })

            if udp_risk_ports:
                result['has_risk'] = True
                result['risk_level'] = 'high'
                result['risk_info'] = f"UDP端口范围 {start_port}-{end_port} 包含 {len(udp_risk_ports)} 个风险端口"
                result['category'] = '禁止使用'

                # 构建详细信息
                port_list = ', '.join(map(str, sorted(udp_risk_ports)[:10]))
                if len(udp_risk_ports) > 10:
                    port_list += f'... (共{len(udp_risk_ports)}个)'

                result['details'] = [f"发现UDP风险端口: {port_list}"]
                result['risk_port_details'] = udp_risk_details[:10]  # 最多显示10个详细信息
            else:
                result['is_safe'] = True
                result['risk_level'] = 'safe'
                result['risk_info'] = f"UDP端口范围 {start_port}-{end_port} 未发现风险端口"

        # 其他协议的处理（如果有的话）
        else:
            # 对于非TCP/UDP协议，进行通用检查
            if end_port - start_port > 1000:
                result['has_risk'] = True
                result['risk_level'] = 'medium'
                result['risk_info'] = f"{protocol}端口范围过大 ({end_port - start_port + 1} 个端口)"
                result['category'] = '受控使用'
                result['details'] = [f"建议缩小端口范围"]
            else:
                # 逐个检查端口
                other_risk_ports = []
                for port in range(start_port, end_port + 1):
                    key = f"{protocol}:{port}"
                    if key in port_risk_map:
                        other_risk_ports.append(port)

                if other_risk_ports:
                    result['has_risk'] = True
                    result['risk_level'] = 'high'
                    result['risk_info'] = f"{protocol}端口范围 {start_port}-{end_port} 包含 {len(other_risk_ports)} 个风险端口"
                    result['category'] = '禁止使用'
                    result['details'] = [f"风险端口: {', '.join(map(str, other_risk_ports[:10]))}{'...' if len(other_risk_ports) > 10 else ''}"]
                else:
                    result['is_safe'] = True
                    result['risk_level'] = 'safe'
                    result['risk_info'] = f"{protocol}端口范围 {start_port}-{end_port} 未发现风险端口"

        return result

    def _generate_risk_summary(self, risk_services: List[Dict], safe_services: List[Dict], unknown_services: List[Dict]) -> str:
        """
        生成风险摘要
        """
        total = len(risk_services) + len(safe_services) + len(unknown_services)

        if total == 0:
            return "无服务信息"

        summary_parts = []

        if risk_services:
            high_risk = sum(1 for rs in risk_services if rs.get('risk_level') == 'high')
            medium_risk = sum(1 for rs in risk_services if rs.get('risk_level') == 'medium')

            if high_risk > 0:
                summary_parts.append(f"🔴 高风险: {high_risk}个")
            if medium_risk > 0:
                summary_parts.append(f"🟡 中风险: {medium_risk}个")

        if safe_services:
            summary_parts.append(f"🟢 安全: {len(safe_services)}个")

        if unknown_services:
            summary_parts.append(f"❓ 未知: {len(unknown_services)}个")

        return f"服务风险检测: {' | '.join(summary_parts)}"

    def get_service_risk_report(self) -> str:
        """
        获取服务风险报告的字符串表示

        Returns:
            str: 格式化的风险报告
        """
        risk_result = self.check_service_risks()

        if 'error' in risk_result:
            return f"❌ 服务风险检测失败: {risk_result['error']}"

        lines = []
        lines.append(f"🔍 服务风险检测报告")
        lines.append(f"总服务数: {risk_result['total_services']}")
        lines.append(f"摘要: {risk_result['summary']}")

        # 高风险服务
        if risk_result['risk_services']:
            lines.append(f"\n🚨 风险服务:")
            for i, risk_service in enumerate(risk_result['risk_services'][:5], 1):
                risk_level_icon = "🔴" if risk_service['risk_level'] == 'high' else "🟡"
                lines.append(f"  {i}. {risk_level_icon} {risk_service['service']}")
                lines.append(f"     分类: {risk_service.get('category', 'N/A')}")
                if risk_service.get('details'):
                    lines.append(f"     详情: {risk_service['details'][0][:100]}...")

            if len(risk_result['risk_services']) > 5:
                lines.append(f"     ... 还有 {len(risk_result['risk_services']) - 5} 个风险服务")

        # 安全服务
        if risk_result['safe_services']:
            lines.append(f"\n✅ 安全服务: {len(risk_result['safe_services'])}个")

        # 未知服务
        if risk_result['unknown_services']:
            lines.append(f"\n❓ 未知服务: {len(risk_result['unknown_services'])}个")
            for unknown in risk_result['unknown_services'][:3]:
                lines.append(f"  - {unknown['service']}")

        return '\n'.join(lines)


class PolicyObjectList:
    """策略对象列表管理器"""
    
    def __init__(self, policies: List[PolicyObject] = None):
        self.policies = policies or []
    
    @classmethod
    def from_json_array(cls, json_array: List[Dict[str, Any]]) -> 'PolicyObjectList':
        """从JSON数组创建策略对象列表"""
        policies = [PolicyObject.from_dict(data) for data in json_array]
        return cls(policies)
    
    def __len__(self) -> int:
        return len(self.policies)
    
    def __iter__(self):
        return iter(self.policies)
    
    def __getitem__(self, index):
        return self.policies[index]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.policies:
            return {}
        
        policy_types = {}
        total_src_addr = 0
        total_dst_addr = 0
        total_services = 0
        actions = {}
        
        for policy in self.policies:
            # 统计策略类型
            policy_type = policy.name_policy_type or "未知"
            policy_types[policy_type] = policy_types.get(policy_type, 0) + 1
            
            # 统计地址和服务
            total_src_addr += policy.src_addr_count
            total_dst_addr += policy.dst_addr_count
            total_services += policy.service_count
            
            # 统计动作
            action = policy.action or "未知"
            actions[action] = actions.get(action, 0) + 1
        
        return {
            'total_policies': len(self.policies),
            'policy_types': policy_types,
            'total_src_addresses': total_src_addr,
            'total_dst_addresses': total_dst_addr,
            'total_services': total_services,
            'actions': actions,
            'avg_src_addr_per_policy': total_src_addr / len(self.policies),
            'avg_dst_addr_per_policy': total_dst_addr / len(self.policies),
            'avg_services_per_policy': total_services / len(self.policies)
        }
    
    def print_summary(self):
        """打印摘要信息"""
        stats = self.get_statistics()
        
        print(f"\n📋 策略列表摘要")
        print("=" * 60)
        print(f"总策略数: {stats['total_policies']}")
        
        print(f"\n策略类型分布:")
        for policy_type, count in stats['policy_types'].items():
            percentage = (count / stats['total_policies']) * 100
            print(f"  {policy_type}: {count} ({percentage:.1f}%)")
        
        print(f"\n执行动作分布:")
        for action, count in stats['actions'].items():
            percentage = (count / stats['total_policies']) * 100
            print(f"  {action}: {count} ({percentage:.1f}%)")
        
        print(f"\n地址和服务统计:")
        print(f"  总源地址数: {stats['total_src_addresses']}")
        print(f"  总目的地址数: {stats['total_dst_addresses']}")
        print(f"  总服务类型数: {stats['total_services']}")
        print(f"  平均每策略源地址: {stats['avg_src_addr_per_policy']:.1f}")
        print(f"  平均每策略目的地址: {stats['avg_dst_addr_per_policy']:.1f}")
        print(f"  平均每策略服务数: {stats['avg_services_per_policy']:.1f}")
    
    def print_detailed(self, max_policies: int = None):
        """打印详细信息"""
        print(f"\n📋 策略详细信息")
        print("=" * 80)
        
        policies_to_show = self.policies[:max_policies] if max_policies else self.policies
        
        for i, policy in enumerate(policies_to_show, 1):
            print(f"\n【策略 {i}】")
            print(policy)
            print("-" * 60)
        
        if max_policies and len(self.policies) > max_policies:
            print(f"\n... 还有 {len(self.policies) - max_policies} 条策略未显示")


# 使用示例
if __name__ == "__main__":
    # 示例数据
    sample_data = [
        {
            "id": "24",
            "name": "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
            "src_zone": "any",
            "src_addr": ["SRC_2023/11_0", "SRC_2023/12_0"],
            "dst_zone": "any",
            "dst_addr": ["any"],
            "service": ["高危端口", "高危端口2"],
            "action": "拒绝",
            "hit_count": "1,362,787"
        }
    ]
    
    # 创建策略对象列表
    policy_list = PolicyObjectList.from_json_array(sample_data)
    
    # 打印摘要和详细信息
    policy_list.print_summary()
    policy_list.print_detailed()
