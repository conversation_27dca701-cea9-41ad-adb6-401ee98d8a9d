"""
测试聊天对象的方法
"""

def test_chat_methods():
    """测试聊天对象的方法"""
    
    print("🧪 测试聊天对象的方法")
    print("=" * 80)
    
    try:
        from ragflow_sdk import RAGFlow
        
        client = RAGFlow(
            api_key="ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
            base_url="http://10.100.98.181:8011"
        )
        
        # 创建聊天会话（使用时间戳确保唯一性）
        import time
        chat_name = f"测试会话_{int(time.time())}"
        chat = client.create_chat(name=chat_name)
        print(f"✅ 创建聊天会话成功: {chat.get('id')}")
        
        # 查看聊天对象的方法
        print(f"\n📋 聊天对象类型: {type(chat)}")
        
        # 获取聊天对象的所有方法
        if hasattr(chat, '__dict__'):
            print(f"📋 聊天对象属性: {list(chat.__dict__.keys())}")
        
        methods = [method for method in dir(chat) if not method.startswith('_')]
        print(f"📋 聊天对象可用方法:")
        for method in methods:
            print(f"  - {method}")
        
        # 尝试发送消息的不同方法
        test_message = "请分析以下策略申请：申请人: 张三, 目的端口: 3306, 协议: TCP"
        
        # 方法1: 尝试 ask
        if hasattr(chat, 'ask'):
            try:
                import inspect
                sig = inspect.signature(chat.ask)
                print(f"\n📋 ask方法签名: {sig}")
                
                response = chat.ask(test_message)
                print(f"✅ ask方法成功: {type(response)}")
                print(f"📋 响应内容: {response}")
            except Exception as e:
                print(f"❌ ask方法失败: {str(e)}")
        
        # 方法2: 尝试 send_message
        if hasattr(chat, 'send_message'):
            try:
                response = chat.send_message(test_message)
                print(f"✅ send_message方法成功: {response}")
            except Exception as e:
                print(f"❌ send_message方法失败: {str(e)}")
        
        # 方法3: 尝试 completion
        if hasattr(chat, 'completion'):
            try:
                response = chat.completion(test_message)
                print(f"✅ completion方法成功: {response}")
            except Exception as e:
                print(f"❌ completion方法失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    test_chat_methods()
