"""
测试中风险策略详细显示功能
"""

import asyncio
from routers.form_router import handle_form_validation


async def test_medium_risk_display():
    """测试中风险策略详细显示"""
    
    print("🧪 测试中风险策略详细显示功能")
    print("=" * 80)
    
    # 模拟包含中风险端口的策略数据
    test_request = {
        "type": "policy_data",
        "count": 3,
        "data": [
            {
                "id": "MED_001",
                "name": "FTP服务访问策略-受控使用",
                "src_zone": "internal",
                "src_addr": ["***********/24"],
                "dst_zone": "dmz",
                "dst_addr": ["172.16.1.100"],
                "service": ["TCP 21", "TCP 22"],  # FTP, SSH - 受控使用
                "action": "允许",
                "hit_count": "3500"
            },
            {
                "id": "MED_002", 
                "name": "Web服务管理策略",
                "src_zone": "management",
                "src_addr": ["10.10.10.0/24"],
                "dst_zone": "dmz",
                "dst_addr": ["172.16.2.0/24"],
                "service": ["TCP 80", "TCP 443", "TCP 23"],  # HTTP, HTTPS, Telnet
                "action": "允许",
                "hit_count": "8000"
            },
            {
                "id": "MED_003",
                "name": "邮件服务访问策略",
                "src_zone": "internal",
                "src_addr": ["192.168.0.0/16"],
                "dst_zone": "external",
                "dst_addr": ["any"],
                "service": ["TCP 110", "TCP 143", "TCP 993"],  # POP3, IMAP, IMAPS
                "action": "允许",
                "hit_count": "1200"
            }
        ]
    }
    
    print("📊 测试数据包含:")
    print("  - FTP/SSH服务 (受控使用端口)")
    print("  - Web服务管理 (HTTP/HTTPS + Telnet)")
    print("  - 邮件服务 (POP3/IMAP相关端口)")
    print()
    
    # 调用处理函数
    try:
        result = await handle_form_validation(test_request)
        print(f"\n✅ 处理完成")
        print(f"返回结果: {result}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_mixed_risk_levels():
    """测试混合风险级别的策略"""
    
    print("\n" + "="*80)
    print("🔍 混合风险级别策略测试")
    print("="*80)
    
    # 包含高风险、中风险、安全端口的混合策略
    test_request = {
        "type": "policy_data",
        "count": 2,
        "data": [
            {
                "id": "MIX_001",
                "name": "混合风险级别策略-高中低",
                "src_zone": "any",
                "src_addr": ["any"],
                "dst_zone": "any",
                "dst_addr": ["any"],
                "service": [
                    "TCP 0",        # 高风险 - 无效端口
                    "TCP 21",       # 中风险 - FTP受控使用
                    "TCP 22",       # 中风险 - SSH受控使用
                    "TCP 8080",     # 安全 - 常用Web端口
                    "UDP 31337",    # 高风险 - Back Orifice木马
                    "TCP 443",      # 中风险 - HTTPS受控使用
                    "ICMP"          # 安全 - 协议
                ],
                "action": "允许",
                "hit_count": "15000"
            },
            {
                "id": "MIX_002",
                "name": "服务器管理策略-多端口",
                "src_zone": "management",
                "src_addr": ["10.0.0.0/8"],
                "dst_zone": "servers",
                "dst_addr": ["172.16.0.0/16"],
                "service": [
                    "TCP 23",       # 中风险 - Telnet受控使用
                    "TCP 80",       # 中风险 - HTTP受控使用
                    "TCP 110",      # 中风险 - POP3受控使用
                    "TCP 143",      # 中风险 - IMAP受控使用
                    "TCP 993",      # 中风险 - IMAPS受控使用
                    "TCP 995"       # 中风险 - POP3S受控使用
                ],
                "action": "允许",
                "hit_count": "5500"
            }
        ]
    }
    
    print("📊 混合测试数据包含:")
    print("  - 策略1: 高风险(无效端口,木马) + 中风险(FTP,SSH,HTTPS) + 安全(Web,ICMP)")
    print("  - 策略2: 全部中风险端口(Telnet,HTTP,邮件服务)")
    print()
    
    # 调用处理函数
    try:
        result = await handle_form_validation(test_request)
        print(f"\n✅ 处理完成")
        print(f"返回结果: {result}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_individual_medium_risk_ports():
    """测试单个中风险端口的详细信息"""
    
    print("\n" + "="*80)
    print("🔍 单个中风险端口详细信息测试")
    print("="*80)
    
    from models.policy_object import PolicyObject
    
    # 测试各种中风险端口
    medium_risk_ports = [
        ("TCP 21", "FTP服务"),
        ("TCP 22", "SSH服务"),
        ("TCP 23", "Telnet服务"),
        ("TCP 80", "HTTP服务"),
        ("TCP 110", "POP3服务"),
        ("TCP 143", "IMAP服务"),
        ("TCP 443", "HTTPS服务"),
        ("TCP 993", "IMAPS服务"),
        ("TCP 995", "POP3S服务")
    ]
    
    for service, description in medium_risk_ports:
        print(f"\n📋 {description} ({service}):")
        print("-" * 50)
        
        policy_data = {
            "id": f"TEST_{service.replace(' ', '_')}",
            "name": f"{description}测试策略",
            "src_zone": "internal",
            "src_addr": ["***********/24"],
            "dst_zone": "external",
            "dst_addr": ["any"],
            "service": [service],
            "action": "允许",
            "hit_count": "1000"
        }
        
        policy = PolicyObject.from_dict(policy_data)
        risk_result = policy.check_service_risks()
        
        print(f"风险级别: {risk_result.get('risk_services', [{}])[0].get('risk_level', 'unknown') if risk_result.get('risk_services') else 'safe'}")
        print(f"检测结果: {risk_result['summary']}")
        
        if risk_result.get('risk_services'):
            risk_service = risk_result['risk_services'][0]
            if risk_service.get('risk_port_details'):
                port_detail = risk_service['risk_port_details'][0]
                print(f"分类: {port_detail['category']}")
                if port_detail['risk']:
                    print(f"风险描述: {port_detail['risk']}")


if __name__ == "__main__":
    asyncio.run(test_medium_risk_display())
    asyncio.run(test_mixed_risk_levels())
    asyncio.run(test_individual_medium_risk_ports())
