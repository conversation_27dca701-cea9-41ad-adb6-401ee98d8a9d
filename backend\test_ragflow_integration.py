"""
测试RAGFlow智能体集成功能
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.policy_object import PolicyObject


def test_ragflow_integration():
    """测试RAGFlow智能体集成"""
    
    print("🧪 测试RAGFlow智能体集成功能")
    print("=" * 80)
    
    # 模拟策略数据
    policy_data = [
        {
            "id": "001",
            "name": "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
            "src_addr": ["192.168.1.0/24"],
            "dst_addr": ["any"],
            "service": ["TCP 80", "TCP 443"],
            "action": "允许"
        },
        {
            "id": "002", 
            "name": "基础策略-主动防御封禁-IN",
            "src_addr": ["any"],
            "dst_addr": ["10.0.0.0/8"],
            "service": ["TCP 135", "TCP 139"],
            "action": "拒绝"
        },
        {
            "id": "003",
            "name": "240816-省内地址-产业园微碳慧能主题网站",
            "src_addr": ["172.16.0.0/16"],
            "dst_addr": ["203.0.113.0/24"],
            "service": ["TCP 443", "TCP 80"],
            "action": "允许"
        }
    ]
    
    print(f"📊 测试数据: {len(policy_data)} 个策略")
    print()
    
    try:
        # 1. 模拟JSON格式化过程
        print("🔄 步骤1: 格式化策略数据...")
        
        # 解析为策略对象
        policy_objects = []
        for item in policy_data:
            policy_obj = PolicyObject.from_dict(item)
            policy_objects.append(policy_obj)
        
        # 构造JSON
        json_objects = []
        for policy_obj in policy_objects:
            json_obj = {
                "id": policy_obj.id,
                "策略名称": policy_obj.name,
                "源地址": policy_obj.src_addr,
                "目的地址": policy_obj.dst_addr
            }
            json_objects.append(json_obj)
        
        # 格式化为字符串
        formatted_json_lines = []
        for obj in json_objects:
            obj_json = json.dumps(obj, ensure_ascii=False, separators=(',', ': '))
            formatted_json_lines.append(f" {obj_json}")
        
        formatted_json = ",\n".join(formatted_json_lines) + ",\n;"
        
        print("✅ JSON格式化完成")
        print(f"📋 格式化的JSON:")
        print("-" * 60)
        print(formatted_json)
        print("-" * 60)
        
        # 2. 测试RAGFlow服务配置
        print(f"\n🔄 步骤2: 测试RAGFlow服务配置...")
        
        try:
            from services.ragflow_service import policy_analysis_service, Config
            
            print("✅ RAGFlow服务导入成功")
            print(f"📋 配置信息:")
            print(f"  - Base URL: {policy_analysis_service.config.RAGFLOW_BASE_URL}")
            print(f"  - API Key: {policy_analysis_service.config.RAGFLOW_API_KEY[:20]}...")
            print(f"  - Assistant ID: {policy_analysis_service.config.RAGFLOW_ASSISTANT_ID}")
            
            # 检查客户端状态
            if policy_analysis_service._rag_client:
                print("✅ RAGFlow客户端已初始化")
            else:
                print("⚠️ RAGFlow客户端未初始化（可能是SDK未安装）")
            
        except ImportError as e:
            print(f"❌ RAGFlow服务导入失败: {str(e)}")
            return
        
        # 3. 测试智能体调用（如果SDK可用）
        print(f"\n🔄 步骤3: 测试智能体调用...")
        
        try:
            # 尝试调用智能体
            analysis_result = policy_analysis_service.process_formatted_json(formatted_json)
            
            print("✅ 智能体调用成功")
            print(f"📊 分析结果:")
            print("=" * 80)
            print(analysis_result)
            print("=" * 80)
            
        except Exception as e:
            print(f"⚠️ 智能体调用失败: {str(e)}")
            print("这可能是由于以下原因:")
            print("  - RAGFlow SDK未安装")
            print("  - 网络连接问题")
            print("  - 智能体ID不存在")
            print("  - API密钥无效")
        
        print(f"\n✅ 集成测试完成!")
        print(f"📋 测试总结:")
        print(f"  - JSON格式化: ✅ 正常")
        print(f"  - 服务配置: ✅ 正常")
        print(f"  - 智能体调用: 取决于SDK和网络环境")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_service_configuration():
    """测试服务配置"""
    
    print(f"\n" + "="*80)
    print("🔧 测试服务配置")
    print("="*80)
    
    try:
        from services.ragflow_service import Config, RAGFlowService
        
        # 测试配置创建
        config = Config(
            api_key="ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm",
            base_url="http://10.100.98.181:8011",
            assistant_id="cbaecd286f9211f0b4040242ac150003"
        )
        
        print("✅ 配置创建成功")
        print(f"  - API Key: {config.RAGFLOW_API_KEY}")
        print(f"  - Base URL: {config.RAGFLOW_BASE_URL}")
        print(f"  - Assistant ID: {config.RAGFLOW_ASSISTANT_ID}")
        
        # 测试服务创建
        service = RAGFlowService(config)
        print("✅ 服务创建成功")
        
        # 测试方法存在性
        if hasattr(service, 'process_formatted_json'):
            print("✅ process_formatted_json 方法存在")
        else:
            print("❌ process_formatted_json 方法不存在")
            
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")


if __name__ == "__main__":
    test_ragflow_integration()
    test_service_configuration()
