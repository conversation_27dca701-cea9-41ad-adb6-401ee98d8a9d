#!/usr/bin/env python3
"""
测试简化后的RAGFlow服务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    try:
        from services.ragflow_service import RAGFlowService, Config, default_config, default_service
        print("✅ RAGFlow服务导入成功")
        return True
    except Exception as e:
        print(f"❌ RAGFlow服务导入失败: {str(e)}")
        return False

def test_config():
    """测试配置"""
    try:
        from services.ragflow_service import Config, default_config

        # 测试默认配置
        assert hasattr(default_config, 'RAGFLOW_API_KEY')
        assert hasattr(default_config, 'RAGFLOW_BASE_URL')
        assert hasattr(default_config, 'RAGFLOW_ASSISTANT_ID')

        print("✅ 默认配置测试通过")
        print(f"   RAGFlow Base URL: {default_config.RAGFLOW_BASE_URL}")
        print(f"   Assistant ID: {default_config.RAGFLOW_ASSISTANT_ID}")

        # 测试自定义配置
        custom_config = Config(
            api_key="test-key",
            base_url="http://test:8011",
            assistant_id="test-id"
        )
        assert custom_config.RAGFLOW_API_KEY == "test-key"
        assert custom_config.RAGFLOW_BASE_URL == "http://test:8011"
        assert custom_config.RAGFLOW_ASSISTANT_ID == "test-id"

        print("✅ 自定义配置测试通过")

        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False

def test_service_methods():
    """测试服务方法"""
    try:
        from services.ragflow_service import RAGFlowService, default_service

        # 测试默认服务实例
        assert hasattr(default_service, 'ask_agent')
        assert callable(getattr(default_service, 'ask_agent'))

        # 测试自定义服务实例
        custom_service = RAGFlowService()
        assert hasattr(custom_service, 'ask_agent')
        assert callable(getattr(custom_service, 'ask_agent'))

        print("✅ 服务方法测试通过")
        print("   ask_agent 方法存在且可调用")
        print("   支持默认和自定义服务实例")

        return True
    except Exception as e:
        print(f"❌ 服务方法测试失败: {str(e)}")
        return False

def test_ask_agent_mock():
    """模拟测试ask_agent方法（不实际调用）"""
    try:
        from services.ragflow_service import RAGFlowService, RAGFLOW_AVAILABLE, default_service, Config

        if not RAGFLOW_AVAILABLE:
            print("⚠️ RAGFlow SDK 不可用，跳过实际调用测试")

            # 测试异常处理
            try:
                default_service.ask_agent("测试问题")
                print("❌ 应该抛出异常但没有")
                return False
            except Exception as e:
                if "RAGFlow SDK 不可用" in str(e):
                    print("✅ 正确处理了SDK不可用的情况")

                    # 测试自定义配置的服务
                    custom_config = Config(api_key="test", base_url="http://test", assistant_id="test")
                    custom_service = RAGFlowService(custom_config)
                    try:
                        custom_service.ask_agent("测试问题")
                        print("❌ 自定义服务应该抛出异常但没有")
                        return False
                    except Exception as e2:
                        if "RAGFlow SDK 不可用" in str(e2):
                            print("✅ 自定义服务也正确处理了SDK不可用的情况")
                            return True
                        else:
                            print(f"❌ 自定义服务异常信息不正确: {str(e2)}")
                            return False
                else:
                    print(f"❌ 异常信息不正确: {str(e)}")
                    return False
        else:
            print("✅ RAGFlow SDK 可用，可以进行实际调用")
            # 这里可以添加实际的调用测试，但需要确保服务可用
            return True

    except Exception as e:
        print(f"❌ ask_agent测试失败: {str(e)}")
        return False

def test_multiple_configs():
    """测试多配置支持"""
    try:
        from services.ragflow_service import RAGFlowService, Config

        # 创建不同的配置
        config1 = Config(
            api_key="key1",
            base_url="http://server1:8011",
            assistant_id="agent1"
        )

        config2 = Config(
            api_key="key2",
            base_url="http://server2:8011",
            assistant_id="agent2"
        )

        # 创建不同的服务实例
        service1 = RAGFlowService(config1)
        service2 = RAGFlowService(config2)

        # 验证配置是否正确
        assert service1.config.RAGFLOW_API_KEY == "key1"
        assert service1.config.RAGFLOW_ASSISTANT_ID == "agent1"
        assert service2.config.RAGFLOW_API_KEY == "key2"
        assert service2.config.RAGFLOW_ASSISTANT_ID == "agent2"

        print("✅ 多配置支持测试通过")
        print("   可以创建使用不同配置的服务实例")

        return True
    except Exception as e:
        print(f"❌ 多配置支持测试失败: {str(e)}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        # 测试是否可以从services模块导入
        from services import RAGFlowService, Config, default_config, default_service

        print("✅ 向后兼容性测试通过")
        print("   可以从services模块正确导入")

        return True
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔄 开始测试简化后的RAGFlow服务...")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_import),
        ("配置测试", test_config),
        ("服务方法测试", test_service_methods),
        ("ask_agent方法测试", test_ask_agent_mock),
        ("多配置支持测试", test_multiple_configs),
        ("向后兼容性测试", test_backward_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   ⚠️ {test_name} 测试未通过")
        except Exception as e:
            print(f"   ❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！RAGFlow服务简化成功！")
        print("\n✅ 重构后的功能:")
        print("   - 支持多配置实例")
        print("   - 每个服务实例可以使用不同的智能体")
        print("   - 保留核心的智能体问话功能")
        print("   - 完善的错误处理")
        print("   - 向后兼容性")
        print("\n📝 使用方法:")
        print("   # 使用默认配置")
        print("   from services.ragflow_service import default_service")
        print("   response = default_service.ask_agent('你的问题')")
        print("   ")
        print("   # 使用自定义配置")
        print("   from services.ragflow_service import RAGFlowService, Config")
        print("   config = Config(api_key='your-key', base_url='your-url', assistant_id='your-id')")
        print("   service = RAGFlowService(config)")
        print("   response = service.ask_agent('你的问题')")
    else:
        print("⚠️ 部分测试未通过，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    main()
