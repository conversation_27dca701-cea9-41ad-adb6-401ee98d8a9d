"""
防火墙策略名称解析器
用于解析和标准化防火墙策略名称
"""

import re
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum


class PolicyType(Enum):
    """策略类型枚举"""
    STANDARD = "标准策略"           # 标准格式：日期-来源-目标-负责人-期限
    SIMPLIFIED = "简化策略"         # 简化格式：日期-来源-目标
    BASIC = "基础策略"             # 基础策略：基础策略-类型
    EMERGENCY = "应急策略"          # 应急策略：系统名称-日期-期限
    UNKNOWN = "未知格式"           # 无法识别的格式


@dataclass
class PolicyNameInfo:
    """策略名称信息"""
    original_name: str                    # 原始名称
    policy_type: PolicyType              # 策略类型
    start_date: Optional[str] = None     # 开始日期
    end_date: Optional[str] = None       # 结束日期
    source: Optional[str] = None         # 来源系统/部门
    target: Optional[str] = None         # 目标系统/服务
    owner: Optional[str] = None          # 负责人
    duration: Optional[str] = None       # 期限描述
    category: Optional[str] = None       # 策略分类（基础策略用）
    system_name: Optional[str] = None    # 系统名称（应急策略用）
    confidence: float = 0.0              # 解析置信度 (0-1)


class PolicyNameParser:
    """防火墙策略名称解析器"""
    
    def __init__(self):
        # 日期格式正则表达式
        self.date_patterns = [
            r'(\d{8})',           # 20220519
            r'(\d{6})',           # 240816 (省略20)
            r'(\d{4}\d{2}\d{2})', # 20240507
        ]
        
        # 期限关键词
        self.duration_keywords = ['长期', '临时', '短期', '永久']
        
        # 基础策略关键词
        self.basic_policy_keywords = ['基础策略', '基准策略']
        
        # 应急策略关键词
        self.emergency_keywords = ['应急', '指挥系统', '勿动']
    
    def parse(self, name: str) -> PolicyNameInfo:
        """
        解析策略名称
        
        Args:
            name: 策略名称
            
        Returns:
            PolicyNameInfo: 解析结果
        """
        name = name.strip()
        info = PolicyNameInfo(original_name=name, policy_type=PolicyType.UNKNOWN)
        
        # 按优先级尝试不同的解析方法
        parsers = [
            self._parse_basic_policy,
            self._parse_emergency_policy,
            self._parse_standard_policy,
            self._parse_simplified_policy
        ]
        
        for parser in parsers:
            result = parser(name)
            if result.confidence > info.confidence:
                info = result
        
        return info
    
    def _parse_basic_policy(self, name: str) -> PolicyNameInfo:
        """解析基础策略格式"""
        info = PolicyNameInfo(original_name=name, policy_type=PolicyType.BASIC)
        
        # 检查是否包含基础策略关键词
        for keyword in self.basic_policy_keywords:
            if keyword in name:
                info.confidence = 0.9
                
                # 提取策略分类
                parts = name.split('-')
                if len(parts) >= 2:
                    info.category = '-'.join(parts[1:]).strip()
                else:
                    info.category = name.replace(keyword, '').strip('-').strip()
                
                return info
        
        return info
    
    def _parse_emergency_policy(self, name: str) -> PolicyNameInfo:
        """解析应急策略格式"""
        info = PolicyNameInfo(original_name=name, policy_type=PolicyType.EMERGENCY)

        # 检查是否包含应急关键词
        has_emergency_keyword = any(keyword in name for keyword in self.emergency_keywords)

        if has_emergency_keyword:
            info.confidence = 0.85  # 提高置信度，优先于基础策略
            
            # 提取日期
            dates = self._extract_dates(name)
            if dates:
                info.start_date = dates[0]
            
            # 提取期限
            info.duration = self._extract_duration(name)
            
            # 提取系统名称（去除日期和期限后的部分）
            system_name = name
            for date in dates:
                system_name = system_name.replace(date, '')
            if info.duration:
                system_name = system_name.replace(info.duration, '')
            
            # 清理系统名称
            system_name = re.sub(r'-+', '-', system_name).strip('-')
            info.system_name = system_name
            
            return info
        
        return info
    
    def _parse_standard_policy(self, name: str) -> PolicyNameInfo:
        """解析标准策略格式: 日期-来源-目标-负责人-期限"""
        info = PolicyNameInfo(original_name=name, policy_type=PolicyType.STANDARD)
        
        parts = name.split('-')
        if len(parts) >= 4:
            # 检查第一部分是否为日期
            if self._is_date(parts[0]):
                info.confidence = 0.7
                info.start_date = self._normalize_date(parts[0])
                
                # 分配其他字段
                if len(parts) >= 5:
                    info.source = parts[1].strip()
                    info.target = parts[2].strip()
                    info.owner = parts[3].strip()
                    info.duration = parts[4].strip()
                    
                    # 检查最后一部分是否为期限
                    if info.duration in self.duration_keywords:
                        info.confidence += 0.2
                else:
                    info.source = parts[1].strip()
                    info.target = parts[2].strip()
                    info.owner = parts[3].strip()
                
                return info
        
        return info
    
    def _parse_simplified_policy(self, name: str) -> PolicyNameInfo:
        """解析简化策略格式: 日期-来源-目标"""
        info = PolicyNameInfo(original_name=name, policy_type=PolicyType.SIMPLIFIED)
        
        parts = name.split('-')
        if len(parts) >= 3:
            # 检查第一部分是否为日期
            if self._is_date(parts[0]):
                info.confidence = 0.6
                info.start_date = self._normalize_date(parts[0])
                info.source = parts[1].strip()
                info.target = '-'.join(parts[2:]).strip()
                
                return info
        
        return info
    
    def _extract_dates(self, text: str) -> list:
        """提取文本中的所有日期"""
        dates = []
        for pattern in self.date_patterns:
            matches = re.findall(pattern, text)
            dates.extend(matches)
        return list(set(dates))  # 去重
    
    def _extract_duration(self, text: str) -> Optional[str]:
        """提取期限信息"""
        for keyword in self.duration_keywords:
            if keyword in text:
                return keyword
        return None
    
    def _is_date(self, text: str) -> bool:
        """判断文本是否为日期格式"""
        for pattern in self.date_patterns:
            if re.match(pattern, text):
                return True
        return False
    
    def _normalize_date(self, date_str: str) -> str:
        """标准化日期格式"""
        if len(date_str) == 6:  # 240816 -> 20240816
            return '20' + date_str
        return date_str
    
    def to_dict(self, info: PolicyNameInfo) -> Dict[str, Any]:
        """将解析结果转换为字典"""
        return {
            'original_name': info.original_name,
            'policy_type': info.policy_type.value,
            'start_date': info.start_date,
            'end_date': info.end_date,
            'source': info.source,
            'target': info.target,
            'owner': info.owner,
            'duration': info.duration,
            'category': info.category,
            'system_name': info.system_name,
            'confidence': round(info.confidence, 2)
        }


def test_parser():
    """测试解析器"""
    parser = PolicyNameParser()
    
    test_names = [
        "20220519-盐城公司办公终端-建湖公司网站系统web服务-杨浩然-长期",
        "20220519-盐城公司办公终端-盐城公司运行监控系统业务接口-杨浩然-长期",
        "20231122-省侧work节点服务-边缘云-ldh-长期",
        "240816-省内地址-产业园微碳慧能主题网站",
        "20200815-科数部运维终端-盐城配网电能质量管控运维接口",
        "基础策略-勒索病毒端口",
        "基础策略-主动防御封禁-IN",
        "应急指挥系统基准策略勿动-20240507-长期"
    ]
    
    print("=" * 80)
    print("防火墙策略名称解析测试")
    print("=" * 80)
    
    for name in test_names:
        result = parser.parse(name)
        result_dict = parser.to_dict(result)
        
        print(f"\n原始名称: {name}")
        print(f"策略类型: {result_dict['policy_type']}")
        print(f"置信度: {result_dict['confidence']}")
        
        # 只打印非空字段
        for key, value in result_dict.items():
            if value and key not in ['original_name', 'policy_type', 'confidence']:
                print(f"{key}: {value}")
        
        print("-" * 40)


if __name__ == "__main__":
    test_parser()
