"""
测试日期解析功能
"""

from datetime import datetime
from models.policy_object import PolicyObject

def debug_parse_date(date_str: str):
    """调试版本的日期解析函数"""
    if not date_str:
        return None

    date_str = date_str.strip()
    print(f"\n🔍 调试解析 '{date_str}':")

    # 常见日期格式
    date_formats = [
        '%Y%m%d',  # 20220519
        '%Y-%m-%d',  # 2022-05-19
        '%Y/%m/%d',  # 2022/05/19
        '%Y.%m.%d',  # 2022.05.19
        '%m/%d/%Y',  # 05/19/2022
        '%d/%m/%Y',  # 19/05/2022
    ]

    # 首先尝试标准格式
    for i, fmt in enumerate(date_formats):
        try:
            result = datetime.strptime(date_str, fmt)
            print(f"  格式 {i+1} ('{fmt}') 成功: {result}")
            return result
        except ValueError as e:
            print(f"  格式 {i+1} ('{fmt}') 失败: {str(e)}")
            continue

    # 处理6位简写日期格式 (YYMMDD -> 20YYMMDD)
    if len(date_str) == 6 and date_str.isdigit():
        print(f"  尝试6位格式解析...")
        try:
            # 提取年月日
            year_part = date_str[:2]
            month_part = date_str[2:4]
            day_part = date_str[4:6]

            # 转换为完整年份，假设21世纪（2000-2099）
            full_year = 2000 + int(year_part)

            # 构建完整日期字符串
            full_date_str = f"{full_year}{month_part}{day_part}"
            print(f"    年份部分: '{year_part}' -> {full_year}")
            print(f"    月份部分: '{month_part}'")
            print(f"    日期部分: '{day_part}'")
            print(f"    完整日期字符串: '{full_date_str}'")

            result = datetime.strptime(full_date_str, '%Y%m%d')
            print(f"  6位格式解析成功: {result}")
            return result

        except ValueError as e:
            print(f"  6位格式解析失败: {str(e)}")

    print(f"  所有格式都失败，返回None")
    return None

def test_date_parsing():
    """测试6位日期格式的解析"""

    # 创建一个测试策略对象
    policy = PolicyObject(name="test_policy")

    # 测试用例
    test_cases = [
        ("240725", "2024-07-25"),  # 您提到的问题案例
        ("250831", "2025-08-31"),  # 原问题案例
        ("220519", "2022-05-19"),  # 正常案例
        ("991231", "9999-12-31"),  # 99开头表示永远
        ("000101", "2000-01-01"),  # 边界案例
        ("20250831", "2025-08-31"),  # 8位完整格式
        ("2025-08-31", "2025-08-31"),  # 带分隔符格式
        ("2025/08/31", "2025-08-31"),  # 斜杠分隔符格式
        ("08/31/2025", "2025-08-31"),  # 美式格式
    ]

    print("🧪 测试修复后的日期解析功能")
    print("=" * 60)

    for input_date, expected_output in test_cases:
        try:
            parsed_date = policy._parse_date(input_date)
            if parsed_date:
                actual_output = parsed_date.strftime("%Y-%m-%d")
                status = "✅ 通过" if actual_output == expected_output else "❌ 失败"
                print(f"{status} | 输入: {input_date:12} | 期望: {expected_output} | 实际: {actual_output}")

                if actual_output != expected_output:
                    print(f"    解析后的datetime对象: {parsed_date}")
            else:
                print(f"❌ 失败 | 输入: {input_date:12} | 期望: {expected_output} | 实际: None (解析失败)")
        except Exception as e:
            print(f"❌ 异常 | 输入: {input_date:12} | 期望: {expected_output} | 错误: {str(e)}")

    print("\n" + "=" * 60)
    print("✅ 修复验证完成！")

if __name__ == "__main__":
    test_date_parsing()
