"""
表单处理相关路由
"""

import json
import urllib.parse
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from models import FormRequest, ValidationResponse, KeyValueItem
from models.policy_name_parser import PolicyNameParser
from models.policy_object import PolicyObject, PolicyObjectList
from utils import validate_form_data, PolicyJsonFormatter
from utils.data_type_checker import DataTypeChecker, DataType
from services import (
    get_policy_risk_analysis,
    process_policy_data_with_objects,
    process_form_data,
    policy_analysis_service,
    ragflow_service
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/form", tags=["表单处理"])

# 创建策略名称解析器实例
policy_parser = PolicyNameParser()


@router.post("/validate")
async def handle_form_validation(request_data: dict):
    """
    表单校验接口
    - 使用工具类检查数据类型
    - 将JSON转换为对象数组
    - 构造指定格式的JSON输出
    """
    try:
        # 使用数据类型检查工具（简化输出）
        data_type, analysis = DataTypeChecker.check_data_type(request_data)
        DataTypeChecker.print_analysis_report(data_type, analysis)  # 注释掉详细报告

        print(f"\n🔍 数据类型: {data_type.value}")
        if data_type == DataType.POLICY_DATA:
            policy_count = analysis.get('actual_count', 0)
            print(f"📊 策略数据数量: {policy_count}")

        # 根据数据类型进行处理
        if data_type == DataType.POLICY_DATA:
            if analysis.get("valid", False):
                policy_data = request_data.get("data", [])

                # 使用工具类解析和格式化策略数据
                policy_objects, formatted_json = PolicyJsonFormatter.parse_and_format_policy_data(policy_data)

                # 打印格式化的JSON结果
                PolicyJsonFormatter.print_formatted_json(formatted_json)

                # 调用RAGFlow智能体分析formatted_json
                try:
                    print(f"\n🤖 开始调用RAGFlow智能体分析策略数据...")
                    analysis_result = ragflow_service.process_formatted_json(formatted_json)

                    print(f"\n📊 智能体分析结果:")
                    print("=" * 80)
                    print(analysis_result)
                    print("=" * 80)

                except Exception as e:
                    print(f"⚠️ RAGFlow智能体调用失败: {str(e)}")
                    print("继续执行其他处理逻辑...")


            else:
                print("❌ 策略数据验证失败，跳过处理")

        elif data_type == DataType.FORM_DATA:
            if analysis.get("valid", False):
                form_items = request_data.get("items", [])
                process_form_data(form_items)
            else:
                print("❌ 表单数据验证失败，跳过处理")

        else:
            print("❌ 无法处理的数据类型")

        # 返回成功响应
        return {
            "code": 400,
            "data": analysis_result,
            "msg": "数据处理完成"
        }

    except Exception as e:
        print(f"❌ 处理请求时出错: {str(e)}")
        return {
            "code": 500,
            "data": f"处理出错: {str(e)}",
            "msg": "服务器错误"
        }


@router.post("/risk-analysis")
async def get_risk_analysis(request_data: dict):
    """
    获取风险分析结果接口
    - 与validate接口相同的入参
    - 返回格式化的风险分析结果
    """
    try:
        # 使用数据类型检查工具（简化输出）
        data_type, analysis = DataTypeChecker.check_data_type(request_data)

        # 根据数据类型进行处理
        if data_type == DataType.POLICY_DATA:
            if analysis.get("valid", False):
                policy_data = request_data.get("data", [])
                risk_analysis_result = get_policy_risk_analysis(policy_data)

                return {
                    "code": 200,
                    "data": risk_analysis_result,
                    "msg": "风险分析完成"
                }
            else:
                return {
                    "code": 400,
                    "data": {"error": "策略数据验证失败", "details": analysis.get('errors', [])},
                    "msg": "数据验证失败"
                }

        elif data_type == DataType.FORM_DATA:
            return {
                "code": 400,
                "data": {"error": "此接口仅支持策略数据分析"},
                "msg": "数据类型不支持"
            }

        else:
            return {
                "code": 400,
                "data": {"error": "无法处理的数据类型", "details": analysis.get('error', 'N/A')},
                "msg": "数据类型错误"
            }

    except Exception as e:
        return {
            "code": 500,
            "data": {"error": f"处理出错: {str(e)}"},
            "msg": "服务器错误"
        }

