"""
表单相关的数据模型
"""

from pydantic import BaseModel
from typing import List, Optional


class KeyValueItem(BaseModel):
    """请求体键值对模型"""
    key: str
    value: str


class FormRequest(BaseModel):
    """请求体模型定义"""
    items: List[KeyValueItem]  # 必须包含的键值对列表


class ValidationResponse(BaseModel):
    """响应模型"""
    code: int  # 状态码（200=成功, 400=参数错误, 500=服务器错误）
    data: Optional[str] = None  # 修改建议（校验失败时返回）
    msg: Optional[str] = None  # 提示信息
