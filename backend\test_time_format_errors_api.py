"""
测试时间格式错误信息是否正确集成到风险分析API中
"""

import requests
import json

def test_risk_analysis_with_time_errors():
    """测试风险分析API是否包含时间格式错误信息"""
    
    print("🧪 测试风险分析API中的时间格式错误信息")
    print("=" * 60)
    
    # 创建包含时间格式错误的测试数据
    test_policies = [
        {
            "id": "22",
            "name": "20220409-省公司上海路数据中心机房k8s集群-边缘云地址-杨浩然-2年",
            "src_zone": "internal",
            "src_addr": ["192.168.1.0/24"],
            "dst_zone": "external", 
            "dst_addr": ["any"],
            "service": ["TCP 80"],
            "action": "允许",
            "hit_count": "44"
        },
        {
            "id": "266",
            "name": "250702-盐城市县办公终端-安全缓冲区安全接入网关授权系统-杨浩然-临时",
            "src_zone": "internal",
            "src_addr": ["10.0.0.0/8"],
            "dst_zone": "external",
            "dst_addr": ["any"], 
            "service": ["TCP 443"],
            "action": "允许",
            "hit_count": "50"
        },
        {
            "id": "177",
            "name": "230613-省信通流量开放平台-360天擎数据-杨浩然-9999",
            "src_zone": "internal",
            "src_addr": ["172.16.0.0/16"],
            "dst_zone": "external",
            "dst_addr": ["any"], 
            "service": ["TCP 9999"],
            "action": "允许",
            "hit_count": "25"
        }
    ]
    
    # 构建请求数据
    request_data = {
        "type": "policy_data",
        "data": test_policies,
        "count": len(test_policies)
    }
    
    try:
        # 发送请求到风险分析API
        url = "http://127.0.0.1:8000/form/risk-analysis"
        response = requests.post(url, json=request_data, timeout=30)
        
        print(f"请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应代码: {result.get('code')}")
            print(f"响应消息: {result.get('msg')}")
            
            if result.get('code') == 200:
                data = result.get('data', {})
                
                # 检查统计信息
                stats = data.get('statistics', {})
                print(f"\n📊 统计信息:")
                print(f"  总策略数: {stats.get('total_policies', 0)}")
                print(f"  高风险策略: {stats.get('high_risk_count', 0)}")
                print(f"  中风险策略: {stats.get('medium_risk_count', 0)}")
                print(f"  安全策略: {stats.get('safe_count', 0)}")
                print(f"  时间格式错误: {stats.get('time_format_error_count', 0)}")
                
                # 检查时间格式错误详情
                time_errors = data.get('time_format_errors', [])
                print(f"\n⏰ 时间格式错误详情 (共 {len(time_errors)} 个):")
                
                if time_errors:
                    for i, error in enumerate(time_errors, 1):
                        print(f"\n【错误策略 {i}】")
                        print(f"  策略ID: {error.get('id')}")
                        print(f"  策略名称: {error.get('name')}")
                        print(f"  解析的起始时间: {error.get('start_date')}")
                        print(f"  解析的持续时间: {error.get('duration')}")
                        print(f"  时间状态: {error.get('time_status')}")
                        print(f"  错误原因: {error.get('message')}")
                        
                        warnings = error.get('warnings', [])
                        if warnings:
                            print(f"  具体警告:")
                            for j, warning in enumerate(warnings, 1):
                                print(f"    {j}. {warning}")
                else:
                    print("  未发现时间格式错误")
                
                print(f"\n✅ 测试成功！时间格式错误信息已正确集成到API响应中。")
                
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

if __name__ == "__main__":
    test_risk_analysis_with_time_errors()
