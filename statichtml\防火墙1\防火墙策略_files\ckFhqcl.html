<!DOCTYPE html>
<!-- saved from url=(0063)http://*************:8075/YCXXYWXT/jrsqAction/ckFhqcl?id=252840 -->
<html lang="zh"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
	
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="keywords" content="">
	<meta name="description" content="">
	<title>协同办公查看</title>
	<link href="./bootstrap.min.css" rel="stylesheet">
	<link href="./font-awesome.min.css" rel="stylesheet">
	<!-- bootstrap-table 表格插件样式 -->
	<link href="./bootstrap-table.min.css" rel="stylesheet">
	<link href="./animate.min.css" rel="stylesheet">
	<link href="./style.min.css" rel="stylesheet">
	<link href="./ry-ui.css" rel="stylesheet">

    
    <link href="./bootstrap-datetimepicker.min.css" rel="stylesheet">

<link rel="stylesheet" href="./layer.css" id="layuicss-layer"><link rel="stylesheet" href="./style.css" id="layuicss-thememoonstylecss"></head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <a class="btn btn-success" id="uploadFile" style="margin-left: 5px" onclick="uploadFile()">
        <i class="fa fa-upload"></i> 附件
    </a>
    <form class="form-horizontal m" id="form-edit">
        <div class="form-group">
            <input id="ID" name="ID" class="form-control" type="hidden" value="252840">
            <label class="col-sm-2 control-label ">申请部门：</label>
            <div class="col-sm-4">
                <input id="SQBM" name="SQBMMC" class="form-control" type="text" readonly="" value="设备管理部（生产管控中心）">
            </div>
            <label class="col-sm-2 control-label ">申请日期：</label>
            <div class="col-sm-4">
                <div class="input-group date">
                    <input name="SQSJ" id="SQSJ" value="2025-07-07" class="form-control" type="text" readonly="">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label ">申请人：</label>
            <div class="col-sm-4">
                <input id="SQR" name="SQR" class="form-control" type="text" readonly="" value="陈伟">
            </div>
            <label class="col-sm-2 control-label is-required">申请人联系电话：</label>
            <div class="col-sm-4">
                <input name="LXDH" id="LXDH" class="form-control" type="text" readonly="" value="13515148245">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">目的运行环境：</label>
            <div class="col-sm-4">
                <input name="MDHJ" id="MDHJ" class="form-control" type="text" required="" readonly="" value="生产环境">
            </div>
            <label class="col-sm-2 control-label is-required">源类型：</label>
            <div class="col-sm-4">
                <input name="YLX" id="YLX" class="form-control" type="text" required="" readonly="" value="服务器">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label ">有效期：</label>
            <div class="col-sm-4">
                <div class="input-group date">
                    <input name="kSSJ" id="kSSJ" value="2025-05-29" class="form-control" type="text" readonly="">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-2 control-label ">至：</label>
            <div class="col-sm-4">
                <div class="input-group date">
                    <input name="JSSJ" id="JSSJ" value="2040-12-31" class="form-control" type="text" readonly="">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">源地址：</label>
            <div class="col-sm-4">
                <input name="IP" id="IP" class="form-control" type="text" required="" readonly="" value="************, ************">
            </div>
            <label class="col-sm-2 control-label is-required">源端口：</label>
            <div class="col-sm-4">
                <input name="YWL" id="YWL" class="form-control" type="text" required="" readonly="" value="any">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">目的地址：</label>
            <div class="col-sm-4">
                <input name="MDDZ" id="MDDZ" class="form-control" type="text" required="" readonly="" value="************">
            </div>
            <label class="col-sm-2 control-label is-required">目的端口：</label>
            <div class="col-sm-4">
                <input name="MDDK" id="MDDK" class="form-control" type="text" required="" readonly="" value="12000">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">端口类型：</label>
            <div class="col-sm-4">
                <input name="DKLX" id="DKLX" class="form-control" type="text" required="" readonly="" value="TCP">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">源地址描述：</label>
            <div class="col-sm-10">
                <textarea name="YDZMS" id="YDZMS" class="form-control" style="height: 100px" required="" readonly="">盐城公司生产管控中心1楼机房PMS3.0服务器</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">目的地址描述：</label>
            <div class="col-sm-10">
                <textarea name="MDDZMS" id="MDDZMS" class="form-control" style="height: 100px" required="" readonly="">智能巡视系统巡检模块</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">备注：</label>
            <div class="col-sm-10">
                <textarea name="BZ" id="BZ" class="form-control" type="text" style="height:100px" readonly=""></textarea>
            </div>
        </div>
        <hr>
        <div class="row" style="margin-top: -10px">
            <div class="col-sm-12">
                
                    <div class="bootstrap-table bootstrap3">
      <div class="fixed-table-toolbar"><div class="bs-bars pull-left"></div></div>
      
      <div class="fixed-table-container fixed-height" style="height: 366.65px; padding-bottom: 35px;">
      <div class="fixed-table-header" style="margin-right: 0px;"><table class="table table-bordered table-hover" style="width: 1120px;"><thead class=""><tr><th class="bs-checkbox " style="width: 30px; " data-field="0"><div class="th-inner "><label><input name="btSelectAll" type="checkbox"><span></span></label></div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDHJ"><div class="th-inner ">目的运行环境</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="YLX"><div class="th-inner ">源类型</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="IP"><div class="th-inner ">源地址</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="YWL"><div class="th-inner ">源端口</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDDZ"><div class="th-inner ">目的地址</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDDK"><div class="th-inner ">目的端口</div><div class="fht-cell"></div></th><th style="text-align: center; width: 160px; " data-field="DKLX"><div class="th-inner ">端口类型</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="YDZMS"><div class="th-inner ">源地址描述</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDDZMS"><div class="th-inner ">目的地址描述</div><div class="fht-cell"></div></th></tr></thead></table></div>
      <div class="fixed-table-body">
      <div class="fixed-table-loading table table-bordered table-hover fixed-table-border" style="top: 35px; width: 1120px;">
      <span class="loading-wrap">
      <span class="loading-text">正在努力地加载数据中，请稍候</span>
      <span class="animation-wrap"><span class="animation-dot"></span></span>
      </span>
    
      </div>
      <table id="bootstrap-table" class="table table-bordered table-hover" style="margin-top: -34px;"><thead class=""><tr><th class="bs-checkbox " style="width: 30px; " data-field="0"><div class="th-inner "><label><input name="btSelectAll" type="checkbox"><span></span></label></div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDHJ"><div class="th-inner ">目的运行环境</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="YLX"><div class="th-inner ">源类型</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="IP"><div class="th-inner ">源地址</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="YWL"><div class="th-inner ">源端口</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDDZ"><div class="th-inner ">目的地址</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDDK"><div class="th-inner ">目的端口</div><div class="fht-cell"></div></th><th style="text-align: center; width: 160px; " data-field="DKLX"><div class="th-inner ">端口类型</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="YDZMS"><div class="th-inner ">源地址描述</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="MDDZMS"><div class="th-inner ">目的地址描述</div><div class="fht-cell"></div></th></tr></thead><tbody><tr class="no-records-found"><td colspan="10">没有找到匹配的记录</td></tr></tbody></table><div class="fixed-table-border" style="width: 1120px; height: 294.65px;"></div></div>
      <div class="fixed-table-footer" style="display: none;"></div>
      </div>
      <div class="fixed-table-pagination" style="display: none;"></div>
      </div><div class="clearfix"></div>
                
            </div>
        </div></form>
    
</div>

    <script> var ctx = "\/YCXXYWXT\/"; var lockscreen = null; if(lockscreen){window.top.location=ctx+"lockscreen";} </script>
    <a id="scroll-up" href="javascript:;" class="btn btn-sm display" style="cursor: pointer; position: fixed; right: 15px; bottom: 5px; display: none;"><i class="fa fa-angle-double-up"></i></a>
	<script src="./jquery.min.js.下载"></script>
	<script src="./bootstrap.min.js.下载"></script>
	<!-- bootstrap-table 表格插件 -->
	<script src="./bootstrap-table.min.js.下载"></script>
	<script src="./bootstrap-table-zh-CN.min.js.下载"></script>
	<script src="./bootstrap-table-mobile.js.下载"></script>
	<!-- jquery-validate 表单验证插件 -->
	<script src="./jquery.validate.min.js.下载"></script>
	<script src="./jquery.validate.extend.js.下载"></script>
	<script src="./messages_zh.js.下载"></script>
	<!-- bootstrap-table 表格树插件 -->
	<script src="./bootstrap-table-tree.min.js.下载"></script>
	<!-- 遮罩层 -->
	<script src="./jquery.blockUI.js.下载"></script>
    <script src="./icheck.min.js.下载"></script>
	<script src="./layer.min.js.下载"></script>
	<script src="./layui.min.js.下载"></script>
	<script src="./common.js.下载"></script>
	<script src="./ry-ui.js.下载"></script>


    <script src="./bootstrap-datetimepicker.min.js.下载"></script>

<script src="./jquery.tmpl.js.下载"></script>
<script>
    var prefix = ctx + "jrsqAction";
    var ywsqPrefix = ctx + "ywsqAction";
    var bpwhPrefix = ctx + "bpwhAction";

    var filePrefix = ctx + "lcqdAction";

    function saveHandle() {
        $.ajax({
            url: prefix + "/submitPass",
            type: 'post',
            data: {
                "ID": $('#ID').val(),
                "zt": $('#ZT').val(),
                "ywlx": $('#YWLX').val(),
                "userId": $('#USERID').val(),
                "gzlid": $('#GZLID').val(),
                "bz": $('#SHYJ').val(),
                "SHYJ": $('#SHYJ').val(),
                "SL": $('#SL').val(),
            },
            success: function (res) {
                var parent = activeWindow();
                parent.$.table.refresh();
                $.modal.closeAll();
                $.modal.alertSuccess("提交成功！");
            }
        });
     }

    function rejectHandle() {
        $.ajax({
            url: prefix + "/submitBack",
            type: 'post',
            data: {
                "ID": $('#ID').val(),
                "zt": $('#ZT').val(),
                "ywlx": $('#YWLX').val(),
                "userId": $('#USERID').val(),
                "gzlid": $('#GZLID').val(),
                "bz": $('#SHYJ').val(),
                "SL": $('#SL').val(),
                "BPLX": "1",
                "name": $('#XLID').val()
            },
            success: function (res) {
                var parent = activeWindow();
                parent.$.table.refresh();
                $.modal.closeAll();
                $.modal.alertSuccess("驳回成功！");
            }
        });
    }


    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(ywsqPrefix + "/saveQtxxyw", $('#form-edit').serialize());
        }
    }

    $(function () {
        var data = [];
        const sblyList = [];
        var options = {
            id: "bootstrap-table",
            height: $(window).height() * 0.55,
            data: sblyList,
            editable: true,
            showSearch: false,
            showRefresh: false,
            pagination: false,
            showColumns: false,
            exportUrl: prefix + "/exportExcel",
            importUrl: prefix + "/importxhsqRecords",
            sidePagination: "client",
            modalName: "短号办理",
            columns: [{
                checkbox: true,
                width: '30'
            },
                {
                    field: 'ID',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'MDHJ',
                    title: '目的运行环境',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '目的运行环境',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'YLX',
                    title: '源类型',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '源类型',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'IP',
                    title: '源地址',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '源地址',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'YWL',
                    title: '源端口',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '源端口',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'MDDZ',
                    title: '目的地址',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '目的地址',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'MDDK',
                    title: '目的端口',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '目的端口',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'DKLX',
                    title: '端口类型',
                    width: '160',
                    align: 'center',
                    editable: {
                        type: 'select',
                        source: [
                            {value: 'TCP', text: "TCP"},
                            {value: 'UDP', text: "UDP"},
                            {value: 'ICMP', text: "ICMP"},
                        ],
                        onblur: "submit",
                        showbuttons: false,
                        placeholder: "请选择",
                        emptytext: '请选择',
                        validate: function (value) {
                            // 编辑验证规则
                        }
                    }
                },
                {
                    field: 'YDZMS',
                    title: '源地址描述',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '目的端口',
                        emptytext: '请输入'
                    }
                },
                {
                    field: 'MDDZMS',
                    title: '目的地址描述',
                    width: '150',
                    align: 'center',
                    editable: {
                        type: 'text',
                        title: '目的端口',
                        emptytext: '请输入'
                    }
                }
            ]
        };
        $.table.init(options);

    });


    function uploadFile() {
        var url = ctx + "/accAction/getObjId/"+2+"/"+$('#ID').val();
        openSp("附件上传",url,800,500);
    }

    function openSp(title,url,width,height) {
        var options = {
            url: url,
            title: title,
            width: width,
            height: height,
            btn: 0
        }
        $.modal.openOptions(options);
    }



    function downloadFile() {
        window.location.href = filePrefix+"/exportWord?id="+$("#ID").val()+"&ywlx="+$('#YWLX').val();
    }

    function viewLc() {
        open("审核流程信息", prefix +"/viewLc?ID="+$("#ID").val(), 900, 500, function (index) {
            top.layer.close(index)   // 关闭弹框
            $.table.search();
        });
    }
    // 修改查看页面按钮
    function open(title, url, width, height, callback) {
        // 如果是移动端，就使用自适应大小弹窗
        if ($.common.isMobile()) {
            width = 'auto';
            height = 'auto';
        }
        if ($.common.isEmpty(title)) {
            title = false;
        }
        if ($.common.isEmpty(url)) {
            url = "/404.html";
        }
        if ($.common.isEmpty(width)) {
            width = 800;
        }
        if ($.common.isEmpty(height)) {
            height = ($(window).height() - 50);
        }
        if ($.common.isEmpty(callback)) {
            callback = function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            }
        }
        top.layer.open({
            type: 2,
            area: [width + 'px', height + 'px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: title,
            content: url,
            btn: ['关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: callback,
            cancel: function (index) {
                return true;
            },
            success: function () {
                $(':focus').blur();
            }
        });
    }


</script>



<script id="qxTypeTpl" type="text/x-jquery-tmpl">

</script>
</body></html>